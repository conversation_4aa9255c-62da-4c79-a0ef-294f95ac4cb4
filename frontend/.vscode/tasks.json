{"version": "2.0.0", "tasks": [{"label": "🔧 Install Main UI Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/main-ui"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "dependencies"}}, {"label": "🔧 Install Board Display Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/board-display"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "dependencies"}}, {"label": "start-main-ui-dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/main-ui"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "start-board-display-dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/board-display", "env": {"PORT": "3001"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true, "problemMatcher": {"owner": "vite", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "🏗️ Build Main UI", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/main-ui"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "build"}, "problemMatcher": ["$tsc"]}, {"label": "🏗️ Build Board Display", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/board-display"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "build"}, "problemMatcher": ["$tsc"]}, {"label": "🧪 Test Main UI (Vitest)", "type": "shell", "command": "npm", "args": ["test"], "options": {"cwd": "${workspaceFolder}/main-ui"}, "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🧪 Test Board Display (Vitest)", "type": "shell", "command": "npm", "args": ["test"], "options": {"cwd": "${workspaceFolder}/board-display"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}]}