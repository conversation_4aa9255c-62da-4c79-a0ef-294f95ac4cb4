{"version": "0.2.0", "configurations": [{"name": "🌐 Debug Main UI (Chrome)", "type": "chrome", "request": "launch", "url": "http://localhost:3010", "webRoot": "${workspaceFolder}/main-ui/src", "preLaunchTask": "start-main-ui-dev", "presentation": {"hidden": false, "group": "frontend", "order": 1}}, {"name": "📺 Debug Board Display (Chrome)", "type": "chrome", "request": "launch", "url": "http://localhost:3011", "webRoot": "${workspaceFolder}/board-display/src", "preLaunchTask": "start-board-display-dev", "presentation": {"hidden": false, "group": "frontend", "order": 2}}, {"name": "🔥 Start Main UI (Development)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/main-ui", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "envFile": "${workspaceRoot}/.env", "env": {"API_URL": "http://localhost:4010", "PORT": "3010"}, "console": "integratedTerminal", "restart": true, "presentation": {"hidden": false, "group": "dev-servers", "order": 1}}, {"name": "🔒 Start Main UI (HTTPS)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/main-ui", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev:https"], "envFile": "${workspaceRoot}/.env", "env": {"API_URL": "https://localhost:4010", "PORT": "3010"}, "console": "integratedTerminal", "restart": true, "presentation": {"hidden": false, "group": "dev-servers", "order": 2}}, {"name": "📺 Start Board Display", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/board-display", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "envFile": "${workspaceRoot}/.env", "env": {"API_URL": "http://localhost:4010", "PORT": "3011", "NO_AUTH": "true"}, "console": "integratedTerminal", "restart": true, "presentation": {"hidden": false, "group": "dev-servers", "order": 3}}]}