{"name": "truss-board-display", "version": "1.0.0", "description": "Truss Robotic Management System Board Display", "type": "module", "scripts": {"dev": "NO_AUTH=true vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@kobalte/core": "^0.12.6", "@solidjs/router": "^0.9.1", "@solidjs/start": "^0.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "solid-js": "^1.8.5", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@solidjs/testing-library": "^0.8.5", "@types/node": "^20.8.0", "autoprefixer": "^10.4.16", "jsdom": "^26.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-solid": "^2.7.2", "vitest": "^0.34.6"}}