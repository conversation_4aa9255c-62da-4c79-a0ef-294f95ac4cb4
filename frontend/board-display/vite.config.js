import { defineConfig } from "vite";
import solid from "vite-plugin-solid";

export default defineConfig(({ command, mode }) => {
  // Board Display always uses HTTP due to system requirements
  const port = parseInt(process.env.PORT) || 3001;

  return {
    plugins: [solid()],
    server: {
      port: port,
      host: true,
      https: false, // Always HTTP for Board Display
    },
    build: {
      target: "esnext",
    },
    define: {
      "import.meta.env.API_URL": JSON.stringify(
        process.env.API_URL || "http://localhost:4000"
      ),
      "import.meta.env.NO_AUTH": JSON.stringify(process.env.NO_AUTH === "true"),
    },
  };
});
