# Development Dockerfile for debugging SolidJS Board Display
FROM node:18-alpine

# Install debugging tools
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files
COPY frontend/board-display/package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY frontend/board-display .

# Expose development server port
EXPOSE 3001

# Health check disabled for dev server (Vite dev server doesn't have a reliable health endpoint)
# HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
#   CMD curl -f http://0.0.0.0:3001 || exit 1

# Start the development server
CMD ["npm", "run", "dev"]
