# Development Dockerfile for debugging React Main UI
FROM node:18-alpine

# Install debugging tools
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files
COPY frontend/main-ui/package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY frontend/main-ui .

# Expose development server port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000 || exit 1

# Start the development server
CMD ["npm", "run", "dev"]
