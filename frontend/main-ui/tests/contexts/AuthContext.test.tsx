import { render, screen, fireEvent, waitFor } from '@solidjs/testing-library';
import { Router } from '@solidjs/router';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AuthProvider, useAuth } from '../../src/contexts/AuthContext';
import { createSignal } from 'solid-js';

// Mock the API client
const mockApiClient = {
  post: vi.fn(),
  get: vi.fn(),
};

vi.mock('../../src/utils/api', () => ({
  apiClient: mockApiClient,
}));

// Mock auth utilities
vi.mock('../../src/utils/auth', () => ({
  getStoredToken: vi.fn(),
  setStoredToken: vi.fn(),
  removeStoredToken: vi.fn(),
}));

// Test component that uses the auth context
const TestComponent = () => {
  const { user, isAuthenticated, login, logout, isLoading } = useAuth();
  const [username, setUsername] = createSignal('');
  const [pin, setPin] = createSignal('');

  const handleLogin = async () => {
    try {
      await login(username(), pin());
    } catch (error) {
      console.error('Login failed');
    }
  };

  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated() ? 'Authenticated' : 'Not Authenticated'}
      </div>
      <div data-testid="user-info">
        {user() ? `User: ${user()!.username}` : 'No User'}
      </div>
      <div data-testid="loading-status">
        {isLoading() ? 'Loading' : 'Not Loading'}
      </div>
      <input
        data-testid="username-input"
        value={username()}
        onInput={(e) => setUsername(e.target.value)}
      />
      <input
        data-testid="pin-input"
        value={pin()}
        onInput={(e) => setPin(e.target.value)}
      />
      <button data-testid="login-button" onClick={handleLogin}>
        Login
      </button>
      <button data-testid="logout-button" onClick={logout}>
        Logout
      </button>
    </div>
  );
};

const renderWithAuth = () => {
  return render(() => (
    <Router>
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    </Router>
  ));
};

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('provides initial unauthenticated state', () => {
    renderWithAuth();
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    expect(screen.getByTestId('user-info')).toHaveTextContent('No User');
    expect(screen.getByTestId('loading-status')).toHaveTextContent('Not Loading');
  });

  it('handles successful login', async () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      role: 'User',
      tenantId: 1,
    };

    mockApiClient.post.mockResolvedValue({
      data: {
        access_token: 'mock-token',
        user: mockUser,
      },
    });

    renderWithAuth();
    
    const usernameInput = screen.getByTestId('username-input');
    const pinInput = screen.getByTestId('pin-input');
    const loginButton = screen.getByTestId('login-button');
    
    fireEvent.input(usernameInput, { target: { value: 'testuser' } });
    fireEvent.input(pinInput, { target: { value: '1234' } });
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('User: testuser');
    });

    expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', {
      username: 'testuser',
      pin: '1234',
    });
  });

  it('handles login failure', async () => {
    mockApiClient.post.mockRejectedValue(new Error('Invalid credentials'));

    renderWithAuth();
    
    const usernameInput = screen.getByTestId('username-input');
    const pinInput = screen.getByTestId('pin-input');
    const loginButton = screen.getByTestId('login-button');
    
    fireEvent.input(usernameInput, { target: { value: 'testuser' } });
    fireEvent.input(pinInput, { target: { value: 'wrong' } });
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
      expect(screen.getByTestId('user-info')).toHaveTextContent('No User');
    });
  });

  it('handles logout', async () => {
    // First login
    const mockUser = {
      id: 1,
      username: 'testuser',
      role: 'User',
      tenantId: 1,
    };

    mockApiClient.post.mockResolvedValue({
      data: {
        access_token: 'mock-token',
        user: mockUser,
      },
    });

    renderWithAuth();
    
    const usernameInput = screen.getByTestId('username-input');
    const pinInput = screen.getByTestId('pin-input');
    const loginButton = screen.getByTestId('login-button');
    
    fireEvent.input(usernameInput, { target: { value: 'testuser' } });
    fireEvent.input(pinInput, { target: { value: '1234' } });
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });

    // Now logout
    const logoutButton = screen.getByTestId('logout-button');
    fireEvent.click(logoutButton);

    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    expect(screen.getByTestId('user-info')).toHaveTextContent('No User');
  });
});
