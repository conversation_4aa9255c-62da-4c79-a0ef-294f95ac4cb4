# Frontend Tests

This directory contains all test files for the Truss Robotic Management System frontend application.

## Structure

```
tests/
├── setup.ts              # Test setup and global mocks
├── contexts/              # Tests for React/Solid contexts
│   └── AuthContext.test.tsx
├── pages/                 # Tests for page components
│   └── Login.test.tsx
├── components/            # Tests for reusable components
├── utils/                 # Tests for utility functions
└── README.md             # This file
```

## Testing Framework

- **Test Runner**: Vitest
- **Testing Library**: @solidjs/testing-library
- **Assertions**: Vitest (with jest-dom matchers)
- **Mocking**: Vitest built-in mocking

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

## Writing Tests

### File Naming Convention
- Test files should end with `.test.tsx` or `.spec.tsx`
- Test files should mirror the source file structure
- Example: `src/pages/Login.tsx` → `tests/pages/Login.test.tsx`

### Import Paths
Since tests are in a separate directory, use relative imports to reference source files:

```typescript
// ✅ Correct
import Login from '../../src/pages/Login';
import { AuthProvider } from '../../src/contexts/AuthContext';

// ❌ Incorrect
import Login from '../pages/Login';
```

### Mocking
Use Vitest's built-in mocking capabilities:

```typescript
// Mock external modules
vi.mock('../../src/utils/api', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
  },
}));

// Mock specific functions
const mockLogin = vi.fn();
```

### Test Structure
Follow the Arrange-Act-Assert pattern:

```typescript
it('should handle successful login', async () => {
  // Arrange
  const mockUser = { id: 1, username: 'test' };
  mockApiClient.post.mockResolvedValue({ data: { user: mockUser } });

  // Act
  render(() => <LoginComponent />);
  fireEvent.click(screen.getByRole('button', { name: 'Login' }));

  // Assert
  await waitFor(() => {
    expect(screen.getByText('Welcome test')).toBeInTheDocument();
  });
});
```

## Best Practices

1. **Organize by feature**: Group tests by the feature they're testing
2. **Use descriptive test names**: Test names should clearly describe what is being tested
3. **Mock external dependencies**: Always mock API calls, external libraries, and complex dependencies
4. **Test user interactions**: Focus on testing how users interact with the UI
5. **Avoid implementation details**: Test behavior, not implementation
6. **Keep tests isolated**: Each test should be independent and not rely on other tests
7. **Use proper cleanup**: The setup file handles cleanup automatically, but be mindful of side effects

## Common Patterns

### Testing Components with Context
```typescript
const renderWithAuth = () => {
  return render(() => (
    <Router>
      <AuthProvider>
        <ComponentUnderTest />
      </AuthProvider>
    </Router>
  ));
};
```

### Testing Async Operations
```typescript
await waitFor(() => {
  expect(screen.getByText('Expected text')).toBeInTheDocument();
});
```

### Testing User Input
```typescript
const input = screen.getByLabelText('Username');
fireEvent.input(input, { target: { value: 'testuser' } });
expect(input.value).toBe('testuser');
```
