import { render, screen, fireEvent, waitFor } from "@solidjs/testing-library";
import { Router } from "@solidjs/router";
import { vi, describe, it, expect, beforeEach } from "vitest";
import Login from "../../src/pages/Login";
import { AuthProvider } from "../../src/contexts/AuthContext";

// Mock the API client
vi.mock("../../src/utils/api", () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
  },
}));

// Mock the auth context
const mockLogin = vi.fn();
const mockAuthContext = {
  user: () => null,
  isAuthenticated: () => false,
  login: mockLogin,
  logout: vi.fn(),
  isLoading: () => false,
};

vi.mock("../../src/contexts/AuthContext", async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...(mod as object),
    useAuth: () => mockAuthContext,
  };
});

const renderLogin = () => {
  return render(() => (
    <Router>
      <AuthProvider>
        <Login />
      </AuthProvider>
    </Router>
  ));
};

describe("Login Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders login form", () => {
    renderLogin();

    expect(screen.getByText("Truss Robotic Management System")).toBeTruthy();
    expect(screen.getByText("Sign in to your account")).toBeTruthy();
    expect(screen.getByLabelText("Select User")).toBeTruthy();
    expect(screen.getByLabelText("PIN")).toBeTruthy();
    expect(screen.getByRole("button", { name: "Sign In" })).toBeTruthy();
  });

  it("shows error when submitting without selecting user", async () => {
    renderLogin();

    const submitButton = screen.getByRole("button", { name: "Sign In" });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText("Please select a user and enter your PIN")
      ).toBeTruthy();
    });
  });

  it("shows error when PIN is not 4 digits", async () => {
    renderLogin();

    const userSelect = screen.getByLabelText("Select User");
    const pinInput = screen.getByLabelText("PIN");
    const submitButton = screen.getByRole("button", { name: "Sign In" });

    fireEvent.change(userSelect, { target: { value: "admin" } });
    fireEvent.input(pinInput, { target: { value: "123" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText("PIN must be exactly 4 digits")).toBeTruthy();
    });
  });

  it("calls login function with correct credentials", async () => {
    renderLogin();

    const userSelect = screen.getByLabelText("Select User");
    const pinInput = screen.getByLabelText("PIN");
    const submitButton = screen.getByRole("button", { name: "Sign In" });

    fireEvent.change(userSelect, { target: { value: "admin" } });
    fireEvent.input(pinInput, { target: { value: "0000" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith("admin", "0000");
    });
  });

  it("shows error message when login fails", async () => {
    mockLogin.mockRejectedValue(new Error("Invalid credentials"));
    renderLogin();

    const userSelect = screen.getByLabelText("Select User");
    const pinInput = screen.getByLabelText("PIN");
    const submitButton = screen.getByRole("button", { name: "Sign In" });

    fireEvent.change(userSelect, { target: { value: "admin" } });
    fireEvent.input(pinInput, { target: { value: "0000" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText("Invalid credentials. Please try again.")
      ).toBeTruthy();
    });
  });

  it("restricts PIN input to 4 digits only", () => {
    renderLogin();

    const pinInput = screen.getByLabelText("PIN") as HTMLInputElement;

    fireEvent.input(pinInput, { target: { value: "12345abc" } });

    expect(pinInput.value).toBe("1234");
  });
});
