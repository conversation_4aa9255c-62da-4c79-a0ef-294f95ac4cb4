import fs from "fs";
import path from "path";
import { defineConfig } from "vite";
import solid from "vite-plugin-solid";

export default defineConfig(({ command, mode }) => {
  const isHttps = process.env.HTTPS === "true" || mode === "https";

  return {
    plugins: [solid()],
    server: {
      port: 3000,
      host: true,
      https: isHttps
        ? {
            key: fs.existsSync(
              path.resolve("../../deployment/certs/server.key")
            )
              ? fs.readFileSync(
                  path.resolve("../../deployment/certs/server.key")
                )
              : undefined,
            cert: fs.existsSync(
              path.resolve("../../deployment/certs/server.crt")
            )
              ? fs.readFileSync(
                  path.resolve("../../deployment/certs/server.crt")
                )
              : undefined,
          }
        : false,
    },
    build: {
      target: "esnext",
    },
    define: {
      "import.meta.env.API_URL": JSON.stringify(
        process.env.API_URL ||
          (isHttps ? "https://localhost" : "http://localhost:4000")
      ),
    },
  };
});
