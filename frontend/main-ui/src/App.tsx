import { Component } from "solid-js";
import { Routes, Route, Navigate } from "@solidjs/router";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";

const ProtectedRoute: Component<{ children: any }> = (props) => {
  const { isAuthenticated } = useAuth();

  return <>{isAuthenticated() ? props.children : <Navigate href="/login" />}</>;
};

const App: Component = () => {
  return (
    <AuthProvider>
      <div class="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/login" component={Login} />
          <Route
            path="/dashboard"
            component={() => (
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            )}
          />
          <Route path="/" component={() => <Navigate href="/dashboard" />} />
        </Routes>
      </div>
    </AuthProvider>
  );
};

export default App;
