import { Component, createSignal, createResource, Show, For } from "solid-js";
import { useNavigate } from "@solidjs/router";
import { useAuth } from "../contexts/AuthContext";
import { apiClient } from "../utils/api";

interface User {
  id: number;
  username: string;
  role: string;
  systemId: number;
}

const Login: Component = () => {
  const [selectedUsername, setSelectedUsername] = createSignal("");
  const [pin, setPin] = createSignal("");
  const [error, setError] = createSignal("");
  const { login, isLoading, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Redirect if already authenticated
  if (isAuthenticated()) {
    navigate("/dashboard");
  }

  // Fetch available users for the dropdown
  const [users] = createResource<User[]>(async () => {
    try {
      const response = await apiClient.get("/auth/users");
      return response.data;
    } catch (error) {
      console.error("Failed to fetch users:", error);
      return [];
    }
  });

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    setError("");

    if (!selectedUsername() || !pin()) {
      setError("Please select a user and enter your PIN");
      return;
    }

    if (pin().length !== 4) {
      setError("PIN must be exactly 4 digits");
      return;
    }

    try {
      await login(selectedUsername(), pin());
    } catch (error) {
      setError("Invalid credentials. Please try again.");
    }
  };

  const handlePinInput = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const value = target.value.replace(/\D/g, "").slice(0, 4);
    setPin(value);
    target.value = value;
  };

  return (
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
            <svg
              class="h-8 w-8 text-primary-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Truss Robotic Management System
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            Sign in to your account
          </p>
        </div>

        <form class="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div class="space-y-4">
            <div>
              <label
                for="username"
                class="block text-sm font-medium text-gray-700"
              >
                Select User
              </label>
              <select
                id="username"
                class="select mt-1"
                value={selectedUsername()}
                onChange={(e) => setSelectedUsername(e.target.value)}
                required
              >
                <option value="">Choose a user...</option>
                <Show when={users()}>
                  <For each={users()}>
                    {(user) => (
                      <option value={user.username}>
                        {user.username} ({user.role})
                      </option>
                    )}
                  </For>
                </Show>
              </select>
            </div>

            <div>
              <label for="pin" class="block text-sm font-medium text-gray-700">
                PIN
              </label>
              <input
                id="pin"
                type="password"
                class="input mt-1"
                placeholder="Enter 4-digit PIN"
                maxLength={4}
                pattern="[0-9]{4}"
                onInput={handlePinInput}
                required
              />
            </div>
          </div>

          <Show when={error()}>
            <div class="rounded-md bg-red-50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg
                    class="h-5 w-5 text-red-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-800">{error()}</p>
                </div>
              </div>
            </div>
          </Show>

          <div>
            <button
              type="submit"
              class="btn btn-primary w-full flex justify-center py-3 text-sm font-medium"
              disabled={isLoading()}
            >
              <Show when={isLoading()} fallback="Sign In">
                <svg
                  class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Signing In...
              </Show>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
