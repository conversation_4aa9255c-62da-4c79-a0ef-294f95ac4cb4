import { Component } from "solid-js";
import { useAuth } from "../contexts/AuthContext";
import RoleGuard, { UserRole } from "../components/RoleGuard";

const Dashboard: Component = () => {
  const { user, logout } = useAuth();

  return (
    <div class="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-xl font-semibold text-gray-900">
                Truss Robotic Management System
              </h1>
            </div>
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-700">
                Welcome, {user()?.username} ({user()?.role})
              </span>
              <button onClick={logout} class="btn btn-secondary text-sm">
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div class="bg-white shadow rounded-lg p-6 mb-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              Welcome, {user()?.username}!
            </h2>
            <p class="text-gray-600">
              Role:{" "}
              <span class="font-medium text-primary-600">{user()?.role}</span>
            </p>
            <p class="text-gray-600">
              System ID: <span class="font-medium">{user()?.systemId}</span>
            </p>
          </div>

          {/* Role-based Navigation Cards */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {/* Admin Functions */}
            <RoleGuard
              allowedRoles={[UserRole.SUPER_ADMIN, UserRole.SITE_ADMIN]}
            >
              <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-8 w-8 text-primary-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">
                      User Management
                    </h3>
                    <p class="text-sm text-gray-500">
                      Manage users and permissions
                    </p>
                  </div>
                </div>
              </div>
            </RoleGuard>

            {/* Settings */}
            <RoleGuard
              allowedRoles={[UserRole.SUPER_ADMIN, UserRole.SITE_ADMIN]}
            >
              <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-8 w-8 text-primary-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">
                      System Settings
                    </h3>
                    <p class="text-sm text-gray-500">
                      Configure system parameters
                    </p>
                  </div>
                </div>
              </div>
            </RoleGuard>

            {/* Batch Management */}
            <RoleGuard
              allowedRoles={[
                UserRole.SUPER_ADMIN,
                UserRole.SITE_ADMIN,
                UserRole.USER,
              ]}
            >
              <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-8 w-8 text-primary-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012 2v2M7 7h10"
                      />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">
                      Batch Management
                    </h3>
                    <p class="text-sm text-gray-500">
                      Manage truss batches and files
                    </p>
                  </div>
                </div>
              </div>
            </RoleGuard>

            {/* Monitoring */}
            <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg
                    class="h-8 w-8 text-primary-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    System Monitoring
                  </h3>
                  <p class="text-sm text-gray-500">
                    View system status and logs
                  </p>
                </div>
              </div>
            </div>

            {/* Robotic Control */}
            <RoleGuard
              allowedRoles={[
                UserRole.SUPER_ADMIN,
                UserRole.SITE_ADMIN,
                UserRole.USER,
              ]}
            >
              <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-8 w-8 text-primary-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">
                      Robotic Control
                    </h3>
                    <p class="text-sm text-gray-500">
                      Control and monitor robots
                    </p>
                  </div>
                </div>
              </div>
            </RoleGuard>

            {/* Reports */}
            <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg
                    class="h-8 w-8 text-primary-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-medium text-gray-900">Reports</h3>
                  <p class="text-sm text-gray-500">View production reports</p>
                </div>
              </div>
            </div>
          </div>

          {/* Status Section */}
          <div class="bg-white shadow rounded-lg p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              System Status
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">Online</div>
                <div class="text-sm text-gray-500">System Status</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">0</div>
                <div class="text-sm text-gray-500">Active Batches</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">Ready</div>
                <div class="text-sm text-gray-500">Robot Status</div>
              </div>
            </div>
          </div>

          {/* Development Notice */}
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg
                  class="h-5 w-5 text-blue-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">
                  Development Status
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p>
                    Authentication and base UI with RBAC are complete.
                    Additional features will be implemented in subsequent
                    development phases.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
