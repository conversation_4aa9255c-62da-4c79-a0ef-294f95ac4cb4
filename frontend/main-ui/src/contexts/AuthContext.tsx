import {
  createContext,
  useContext,
  createSignal,
  createEffect,
  ParentComponent,
} from "solid-js";
import { useNavigate } from "@solidjs/router";
import {
  getStoredToken,
  removeStoredToken,
  setStoredToken,
} from "../utils/auth";
import { apiClient } from "../utils/api";

export interface User {
  id: number;
  username: string;
  role: string;
  systemId: number;
}

interface AuthContextType {
  user: () => User | null;
  isAuthenticated: () => boolean;
  login: (username: string, pin: string) => Promise<void>;
  logout: () => void;
  isLoading: () => boolean;
}

const AuthContext = createContext<AuthContextType>();

export const AuthProvider: ParentComponent = (props) => {
  const [user, setUser] = createSignal<User | null>(null);
  const [isLoading, setIsLoading] = createSignal(false);
  const navigate = useNavigate();

  const isAuthenticated = () => !!user();

  const login = async (username: string, pin: string) => {
    setIsLoading(true);
    try {
      const response = await apiClient.post("/auth/login", { username, pin });
      const { access_token, user: userData } = response.data;

      setStoredToken(access_token);
      setUser(userData);
      navigate("/dashboard");
    } catch (error) {
      console.error("Login failed:", error);
      throw new Error("Invalid credentials");
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiClient.post("/auth/logout");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      removeStoredToken();
      setUser(null);
      navigate("/login");
    }
  };

  // Check for existing token on mount
  createEffect(() => {
    const token = getStoredToken();
    if (token && !user()) {
      // Validate token by fetching user profile
      apiClient
        .get("/auth/profile")
        .then((response) => {
          setUser({
            id: response.data.sub,
            username: response.data.username,
            role: response.data.role,
            systemId: response.data.systemId,
          });
        })
        .catch(() => {
          // Token is invalid, remove it
          removeStoredToken();
        });
    }
  });

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    login,
    logout,
    isLoading,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {props.children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
