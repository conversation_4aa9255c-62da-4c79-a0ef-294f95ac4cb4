import { Component, JSX, Show } from 'solid-js';
import { useAuth } from '../contexts/AuthContext';

export enum UserRole {
  SUPER_ADMIN = 'Super Admin',
  SITE_ADMIN = 'Site Admin',
  USER = 'User',
  VIEWER = 'Viewer',
}

interface RoleGuardProps {
  allowedRoles: UserRole[];
  children: JSX.Element;
  fallback?: JSX.Element;
}

const RoleGuard: Component<RoleGuardProps> = (props) => {
  const { user } = useAuth();

  const hasPermission = () => {
    const currentUser = user();
    if (!currentUser) return false;
    return props.allowedRoles.includes(currentUser.role as UserRole);
  };

  return (
    <Show when={hasPermission()} fallback={props.fallback}>
      {props.children}
    </Show>
  );
};

export default RoleGuard;
