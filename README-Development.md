# TOM Development Setup Guide

This guide explains how to set up the TOM (Task Orchestration Manager) project for local development with VS Code debugging support.

## 🏗️ Architecture Overview

The development setup separates **infrastructure** from **application services**:

- **Infrastructure** (Docker): PostgreSQL, Redis
- **Application Services** (Local/VS Code): API, Python microservices, Frontend apps

## 📁 Docker Compose Files

The project uses **3 Docker Compose files** for different purposes:

1. **`docker-compose.yml`** - Infrastructure only (PostgreSQL + Redis) for development
2. **`docker-compose.prod.yml`** - Full application stack for production deployment
3. **`docker-compose.override.yml`** - Development overrides (disables app containers)

## 🚀 Quick Start

### 1. Start Infrastructure Services

```bash
# Start only PostgreSQL and Redis in Docker
cd deployment
docker compose up postgres redis -d
```

### 2. Install Dependencies

```bash
# Install Node.js dependencies
cd backend/api && npm install
cd ../../frontend/main-ui && npm install
cd ../board-display && npm install

# Install Python dependencies (in your virtual environment)
pip install -r backend/logging/requirements.txt
pip install -r backend/settings/requirements.txt
pip install -r backend/file-parsing/requirements.txt
pip install -r backend/rules-engine/requirements.txt
pip install -r backend/robotic-integration/requirements.txt
```

### 3. Run Database Migrations

```bash
cd database
alembic upgrade head
```

### 4. Start Development Environment

**Option A: VS Code Compound Launch (Recommended)**

1. Open the workspace: `TOM.code-workspace`
2. Go to Run and Debug (Ctrl+Shift+D)
3. Select "🚀 Start Full Development Environment"
4. Press F5

**Option B: Manual Service Startup**

```bash
# Terminal 1: API Server
cd backend/api
npm run dev

# Terminal 2: Logging Service
cd backend/logging
python main.py

# Terminal 3: Settings Service
cd backend/settings
python main.py

# Terminal 4: Main UI
cd frontend/main-ui
npm run dev

# Terminal 5: Board Display
cd frontend/board-display
npm run dev
```

## 🐛 Debugging

### VS Code Debug Configurations

Each service has dedicated debug configurations:

**Backend Services:**

- 🚀 Debug Node.js API
- 🔥 Debug Node.js API (Development Mode)
- 🪵 Debug Logging Service
- ⚙️ Debug Settings Service
- 💾 Debug File Parsing Service
- 📜 Debug Rules Engine Service
- 🤖 Debug Robotic Integration Service

**Frontend Services:**

- 🌐 Debug Main UI (Chrome)
- 📺 Debug Board Display (Chrome)
- 🔥 Start Main UI (Development)
- 📺 Start Board Display

**Testing:**

- 🧪 Debug All Python Tests
- 🧪 Debug Integration Tests
- 🧪 Debug Unit Tests
- 🧪 Debug E2E Tests

### Python Debugging with debugpy

Python services support optional debugging with debugpy:

```bash
# Enable debugging for a Python service
export DEBUG_ENABLED=true
python main.py
```

The service will wait for a debugger to attach on the configured port:

- Logging Service: Port 5678
- Settings Service: Port 5679
- File Parsing Service: Port 5680
- Rules Engine Service: Port 5681
- Robotic Integration Service: Port 5682

## 🌐 Service URLs

### Development URLs

- **Main UI**: http://localhost:3010
- **Board Display**: http://localhost:3011
- **API Server**: http://localhost:4010
- **Logging Service**: http://localhost:8011
- **Settings Service**: http://localhost:8012
- **File Parsing Service**: http://localhost:8013
- **Rules Engine Service**: http://localhost:8014
- **Robotic Integration Service**: http://localhost:8015

### Infrastructure URLs

- **PostgreSQL**: localhost:5442
- **Redis**: localhost:6389

## 📁 Project Structure

```
TOM/
├── .env                          # Shared environment variables
├── TOM.code-workspace           # VS Code workspace configuration
├── backend/
│   ├── api/                     # Node.js/NestJS API server
│   ├── logging/                 # Python logging microservice
│   ├── settings/                # Python settings microservice
│   ├── file-parsing/            # Python file parsing microservice
│   ├── rules-engine/            # Python rules engine microservice
│   └── robotic-integration/     # Python robotic integration microservice
├── frontend/
│   ├── main-ui/                 # React/SolidJS main interface
│   └── board-display/           # React/SolidJS board display
├── database/                    # Database migrations and models
├── deployment/
│   ├── docker-compose.yml       # Infrastructure only (dev)
│   ├── docker-compose.prod.yml  # Full stack (production)
│   └── docker-compose.override.yml # Disables app containers in dev
├── shared/                      # Shared code and models
└── tests/                       # Centralized test suite
```

## 🔧 Environment Configuration

The `.env` file contains all shared environment variables. Key variables:

```env
# Database
DB_HOST=localhost
DB_PORT=5442
DATABASE_URL=postgresql://user:password@localhost:5442/tom

# Redis
REDIS_URL=redis://localhost:6389

# API
API_PORT=4010
JWT_SECRET=dev-secret-key-change-in-production

# Python Services
LOGGING_SERVICE_PORT=8011
SETTINGS_SERVICE_PORT=8012
# ... etc

# Debug Ports
DEBUG_API_PORT=9239
DEBUG_LOGGING_PORT=5688
# ... etc
```

## 🧪 Testing

### Run Tests via VS Code

1. Open Run and Debug panel
2. Select test configuration
3. Press F5

### Run Tests via Command Line

```bash
# API Tests (Jest)
cd backend/api && npm test

# Python Tests (Pytest)
cd tests && pytest -v

# Frontend Tests (Vitest)
cd frontend/main-ui && npm test
cd frontend/board-display && npm test
```

## 🐳 Deployment Options

### **Development (Infrastructure Only)**

```bash
cd deployment
docker compose up postgres redis -d
```

### **Production (Full Stack)**

```bash
cd deployment
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### **File Usage Summary**

- **Development**: `docker-compose.yml` + `docker-compose.override.yml` (automatic)
- **Production**: `docker-compose.yml` + `docker-compose.prod.yml` (explicit)
- **Override file**: Automatically applied in development to disable app containers

## 🛠️ VS Code Tasks

Use Ctrl+Shift+P → "Tasks: Run Task" to access:

**Infrastructure:**

- 🐳 Start Infrastructure (Postgres + Redis)
- 🛑 Stop Infrastructure

**Dependencies:**

- 🔧 Install API Dependencies
- 🐍 Install Python Dependencies (All Services)
- 🔧 Install Main UI Dependencies
- 🔧 Install Board Display Dependencies

**Building:**

- 🏗️ Build Main UI
- 🏗️ Build Board Display

**Testing:**

- 🧪 Test API (Jest)
- 🧪 Test Python Services (Pytest)
- 🧪 Test Main UI (Vitest)
- 🧪 Test Board Display (Vitest)

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**: Check if ports 3010, 3011, 4010, 5442, 6389, 8011-8015 are available
2. **Database connection**: Ensure PostgreSQL container is running: `docker ps`
3. **Python path issues**: Check PYTHONPATH in .env file
4. **Node modules**: Run `npm install` in each frontend/backend directory
5. **Python dependencies**: Ensure virtual environment is activated and dependencies installed

### Reset Development Environment

```bash
# Stop all containers
docker compose -f deployment/docker-compose.yml down

# Remove volumes (WARNING: This deletes all data)
docker volume rm deployment_postgres_data deployment_redis_data

# Restart infrastructure
docker compose -f deployment/docker-compose.yml up postgres redis -d
```

## 📚 Additional Resources

- [VS Code Debugging Guide](https://code.visualstudio.com/docs/editor/debugging)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [NestJS Documentation](https://nestjs.com/)
- [SolidJS Documentation](https://www.solidjs.com/)
