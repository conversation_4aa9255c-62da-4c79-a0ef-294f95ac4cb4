from sqlalchemy import (
    Column,
    BigInteger,
    Integer,
    ForeignKey,
    LargeBinary,
    DateTime,
    Index,
)
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship


from .base import Base


class TrussFile(Base):
    __tablename__ = "truss_files"
    __table_args__ = (Index("idx_truss_files_system_id", "system_id", "id"),)

    id = Column(BigInteger, primary_key=True)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)
    file_data = Column(LargeBinary, nullable=False)  # BLOB storage
    created_at = Column(DateTime, server_default=func.now())

    # Relationships
    system = relationship("System")

    def __repr__(self):
        return f"<TrussFile(id={self.id}, size={len(self.file_data) if self.file_data else 0})>"
