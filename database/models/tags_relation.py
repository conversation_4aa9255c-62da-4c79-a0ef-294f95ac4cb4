from sqlalchemy import Column, BigInteger, Integer, ForeignKey, Enum, DateTime, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

import enum

from .base import Base


class EntityType(enum.Enum):
    batch = "batch"


class TagsRelation(Base):
    __tablename__ = "tags_relations"
    __table_args__ = (
        Index("idx_tags_relations_entity_type", "entity_type", "type_id"),
        Index("idx_tags_relations_tag_id", "tag_id"),
    )

    id = Column(BigInteger, primary_key=True)
    entity_type = Column(Enum(EntityType), nullable=False)
    type_id = Column(BigInteger, nullable=False)
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
    created_at = Column(DateTime, server_default=func.now())

    # Relationships
    tag = relationship("Tag")

    def __repr__(self):
        return f"<TagsRelation(id={self.id}, entity_type='{self.entity_type}', type_id={self.type_id})>"
