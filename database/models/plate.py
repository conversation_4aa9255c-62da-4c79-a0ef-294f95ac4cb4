from sqlalchemy import (
    <PERSON>umn,
    BigInteger,
    Integer,
    String,
    Boolean,
    Float,
    ForeignKey,
    JSON,
    DateTime,
    Index,
)
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship


from .base import Base


class Plate(Base):
    __tablename__ = "plates"

    # Partitioning will be handled at the database level
    __table_args__ = (
        Index("idx_plates_truss_system", "truss_id", "system_id"),
        Index("idx_plates_system_id", "system_id"),
        {"postgresql_partition_by": "RANGE (batch_id)"},
    )

    id = Column(BigInteger, primary_key=True)
    truss_id = Column(BigInteger, ForeignKey("trusses.id"), nullable=False)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)

    # Plate identification
    unique_id = Column(String(36))
    label = Column(String(100))
    description = Column(String)
    object_guid = Column(String(36))

    # Physical properties
    gauge = Column(Float)
    plate_height = Column(Float)
    plate_width = Column(Float)

    # Geometry
    vertices = Column(JSON, nullable=False)
    original_vertices = Column(JSON, nullable=False)
    dimensions = Column(JSON, nullable=False)
    original_dimensions = Column(JSON, nullable=False)

    # Position and orientation
    offset = Column(JSON, nullable=False)
    original_offset = Column(JSON, nullable=False)
    center_point = Column(JSON, nullable=False)
    original_center_point = Column(JSON, nullable=False)
    rotation = Column(JSON, nullable=False)
    original_rotation = Column(JSON, nullable=False)

    # File-specific data
    file_center_point = Column(JSON)
    file_rotation = Column(JSON)
    file_vertices = Column(JSON)

    # Manufacturing properties
    manual = Column(Boolean, default=False, nullable=False)

    # Modification tracking
    modifications = Column(JSON, default=list)

    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    modified_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    truss = relationship("Truss", back_populates="plates")
    system = relationship("System")

    def __repr__(self):
        return f"<Plate(id={self.id}, truss_id={self.truss_id}, label='{self.label}')>"
