from sqlalchemy import Column, Integer, String, DateTime, Index
from sqlalchemy.sql import func


from .base import Base


class Tag(Base):
    __tablename__ = "tags"
    __table_args__ = (Index("idx_tags_name", "name"),)

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    created_at = Column(DateTime, server_default=func.now())

    def __repr__(self):
        return f"<Tag(id={self.id}, name='{self.name}')>"
