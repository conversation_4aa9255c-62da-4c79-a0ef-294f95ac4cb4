"""
Database models for the Task Orchestration Manager (TOM).

This module contains all SQLAlchemy models for the system, including:
- System management (multi-system support)
- User authentication and authorization
- Logging and audit trails
- Settings management
- Tag system with normalized relations
- Truss file management with BLOB storage
- Batch and truss management
- Board and plate data with modification tracking
- Build snapshots for robotic integration

All models follow the naming conventions:
- Primary keys are named 'id'
- Foreign keys are descriptively named (e.g., 'system_id')
- Column names use snake_case
- Tables support multi-system isolation via system_id
"""

from .system import System
from .user import User
from .log import Log
from .setting import Setting
from .tag import Tag
from .tags_relation import TagsRelation, EntityType
from .truss_file import TrussFile
from .batch import Batch
from .truss import Truss
from .board import Board
from .plate import Plate
from .truss_build import TrussBuild

# Import the base for creating tables
from .base import Base

__all__ = [
    # Core models
    "System",
    "User",
    "Log",
    "Setting",
    # Tag system
    "Tag",
    "TagsRelation",
    "EntityType",
    # File and batch management
    "TrussFile",
    "Batch",
    "Truss",
    # Board and plate data
    "Board",
    "Plate",
    # Build tracking
    "TrussBuild",
    # SQLAlchemy base
    "Base",
]
