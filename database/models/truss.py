from sqlalchemy import Column, BigInteger, Integer, ForeignKey, JSON, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship


from .base import Base


class Truss(Base):
    __tablename__ = "trusses"

    id = Column(BigInteger, primary_key=True)
    batch_id = Column(BigInteger, ForeignKey("batches.id"), nullable=False)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)
    metadata = Column(JSON)
    created_at = Column(DateTime, server_default=func.now())
    modified_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    batch = relationship("Batch", back_populates="trusses")
    system = relationship("System")
    boards = relationship("Board", back_populates="truss")
    plates = relationship("Plate", back_populates="truss")
    builds = relationship("TrussBuild", back_populates="truss")

    def __repr__(self):
        return f"<Truss(id={self.id}, batch_id={self.batch_id})>"
