from sqlalchemy import (
    <PERSON>umn,
    BigInteger,
    Integer,
    String,
    Boolean,
    Float,
    ForeignKey,
    JSON,
    DateTime,
    Index,
)
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship


from .base import Base


class Board(Base):
    __tablename__ = "boards"

    # Partitioning will be handled at the database level
    __table_args__ = (
        Index("idx_boards_truss_system", "truss_id", "system_id"),
        Index("idx_boards_system_id", "system_id"),
        {"postgresql_partition_by": "RANGE (batch_id)"},
    )

    id = Column(BigInteger, primary_key=True)
    truss_id = Column(BigInteger, ForeignKey("trusses.id"), nullable=False)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)

    # Board identification
    piece_type = Column(String(50))
    label = Column(String(100))
    name = Column(String(100))
    object_guid = Column(String(36))

    # Dimensions (stored as <PERSON><PERSON><PERSON> for flexibility)
    dimensions = Column(JSON, nullable=False)
    original_dimensions = Column(JSON, nullable=False)

    # Geometry
    vertices = Column(JSON, nullable=False)
    original_vertices = Column(JSON, nullable=False)

    # Material properties
    member_grade = Column(String(50))
    member_species = Column(String(50))
    weight = Column(Float)

    # Manufacturing properties
    manual = Column(Boolean, default=False, nullable=False)
    direction = Column(JSON)
    place_location = Column(JSON)
    plate_edges = Column(JSON)
    pick_offset = Column(JSON)
    angle = Column(Float)
    angle_limits = Column(JSON)

    # Plate relationships
    attached_plates = Column(JSON)  # Array of plate IDs
    intersecting_plates = Column(JSON)  # Array of plate IDs
    splice_plate_id = Column(BigInteger)
    center_plate_ids = Column(JSON)  # Array of plate IDs

    # File-specific data
    file_vertices = Column(JSON)
    file_rotation = Column(JSON)
    file_order = Column(Integer)

    # Labels and notes
    print_label = Column(String(100))
    notes = Column(String)

    # Modification tracking
    modifications = Column(JSON, default=list)

    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    modified_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    truss = relationship("Truss", back_populates="boards")
    system = relationship("System")

    def __repr__(self):
        return f"<Board(id={self.id}, truss_id={self.truss_id}, label='{self.label}')>"
