from sqlalchemy import Column, BigInteger, Integer, String, ForeignKey, DateTime, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from .base import Base


class Batch(Base):
    __tablename__ = "batches"
    __table_args__ = (Index("idx_batches_system_id", "system_id", "id"),)

    id = Column(BigInteger, primary_key=True)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(String)
    status = Column(
        String(20), nullable=False
    )  # active, processing, completed, cancelled
    created_at = Column(DateTime, server_default=func.now())
    modified_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    system = relationship("System")
    trusses = relationship("Truss", back_populates="batch")

    def __repr__(self):
        return f"<Batch(id={self.id}, name='{self.name}', status='{self.status}')>"
