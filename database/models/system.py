from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from .base import Base


class System(Base):
    __tablename__ = "systems"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)

    # Relationships
    users = relationship("User", back_populates="system")
    logs = relationship("Log", back_populates="system")

    def __repr__(self):
        return f"<System(id={self.id}, name='{self.name}')>"
