from sqlalchemy import Column, <PERSON>te<PERSON>, String, ForeignKey, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from .base import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)
    username = Column(String(100), nullable=False)
    pin = Column(String(4), nullable=False)
    role = Column(String(50), nullable=False)  # Super Admin, Site Admin, User, Viewer
    created_at = Column(DateTime, server_default=func.now())
    modified_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Relationships
    system = relationship("System", back_populates="users")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
