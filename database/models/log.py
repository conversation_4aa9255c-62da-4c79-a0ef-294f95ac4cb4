from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from .base import Base


class Log(Base):
    __tablename__ = "logs"

    id = Column(Integer, primary_key=True)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    role = Column(String(50), nullable=True)
    action_type = Column(String(50), nullable=True)
    target_data = Column(
        JSON, nullable=True
    )  # Use JSON for compatibility, JSONB in PostgreSQL
    timestamp = Column(DateTime, server_default=func.now())

    # Relationships
    system = relationship("System")
    user = relationship("User")

    def __repr__(self):
        return f"<Log(id={self.id}, action='{self.action_type}', timestamp='{self.timestamp}')>"
