from sqlalchemy import <PERSON>umn, Big<PERSON><PERSON>ger, Integer, ForeignKey, JSON, DateTime, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship


from .base import Base


class TrussBuild(Base):
    __tablename__ = "truss_builds"
    __table_args__ = (Index("idx_truss_builds_truss_system", "truss_id", "system_id"),)

    id = Column(BigInteger, primary_key=True)
    truss_id = Column(BigInteger, ForeignKey("trusses.id"), nullable=False)
    system_id = Column(Integer, ForeignKey("systems.id"), nullable=False)
    build_timestamp = Column(DateTime, server_default=func.now())
    boards_snapshot = Column(JSON, nullable=False)  # Snapshot of boards at build time
    plates_snapshot = Column(JSON, nullable=False)  # Snapshot of plates at build time
    robot_data = Column(JSON, nullable=False)  # Robot-specific data

    # Relationships
    truss = relationship("Truss", back_populates="builds")
    system = relationship("System")

    def __repr__(self):
        return f"<TrussBuild(id={self.id}, truss_id={self.truss_id}, timestamp='{self.build_timestamp}')>"
