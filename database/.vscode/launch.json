{"version": "0.2.0", "configurations": [{"name": "🐳 Start Infrastructure (Postgres + Redis)", "type": "node", "request": "launch", "runtimeExecutable": "docker", "runtimeArgs": ["compose", "-f", "../deployment/docker-compose.yml", "up", "-d", "postgres", "redis"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "presentation": {"hidden": false, "group": "infrastructure", "order": 1}}, {"name": "🛑 Stop Infrastructure", "type": "node", "request": "launch", "runtimeExecutable": "docker", "runtimeArgs": ["compose", "-f", "../deployment/docker-compose.yml", "down"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "presentation": {"hidden": false, "group": "infrastructure", "order": 2}}]}