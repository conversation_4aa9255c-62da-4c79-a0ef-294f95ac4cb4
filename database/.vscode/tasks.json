{"version": "2.0.0", "tasks": [{"label": "🐳 Start Infrastructure (Postgres + Redis)", "type": "shell", "command": "docker", "args": ["compose", "-f", "../deployment/docker-compose.yml", "up", "-d", "postgres", "redis"], "options": {"cwd": "${workspaceFolder}"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "infrastructure"}, "problemMatcher": []}, {"label": "🛑 Stop Infrastructure", "type": "shell", "command": "docker", "args": ["compose", "-f", "../deployment/docker-compose.yml", "down"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "infrastructure"}, "problemMatcher": []}, {"label": "🔄 Run Database Migrations", "type": "shell", "command": "alembic", "args": ["upgrade", "head"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5434/tom"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "database"}, "problemMatcher": []}, {"label": "📝 Create Migration", "type": "shell", "command": "alembic", "args": ["revision", "--autogenerate", "-m", "${input:migrationMessage}"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5434/tom"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "database"}, "problemMatcher": []}], "inputs": [{"id": "migrationMessage", "description": "Migration message", "default": "Auto-generated migration", "type": "promptString"}]}