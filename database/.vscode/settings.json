{
  // Python Configuration for Alembic
  "python.defaultInterpreterPath": "../venv/bin/python",
  "python.terminal.activateEnvironment": true,

  // File Associations
  "files.associations": {
    "*.sql": "sql",
    "alembic.ini": "ini",
    "*.py": "python"
  },

  // SQL Tools Configuration
  "sqltools.connections": [
    {
      "name": "TOM Development Database",
      "driver": "PostgreSQL",
      "previewLimit": 50,
      "server": "localhost",
      "port": 5434,
      "database": "tom",
      "username": "user",
      "password": "password"
    }
  ],

  // Search Configuration
  "search.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    "**/versions/__pycache__": true
  },

  // File Watcher Configuration
  "files.watcherExclude": {
    "**/__pycache__/**": true
  },

  // Terminal Configuration
  "terminal.integrated.env.linux": {
    "DATABASE_URL": "postgresql://user:password@localhost:5434/tom",
    "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/shared/python"
  },

  // Editor Configuration for SQL
  "[sql]": {
    "editor.defaultFormatter": "ms-mssql.mssql"
  },

  // Python formatting
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.formatOnSave": true
  }
}
