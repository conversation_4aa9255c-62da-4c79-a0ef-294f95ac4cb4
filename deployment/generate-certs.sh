#!/bin/bash

# Generate self-signed SSL certificates for TOM (Task Orchestration Manager)
# These certificates are used for HTTPS communication in production environment
# where systems are accessed by IP on local networks without internet access

echo "Generating self-signed SSL certificates for TOM..."

# Generate private key
openssl genrsa -out server.key 2048

# Generate certificate signing request
openssl req -new -key server.key -out server.csr -subj "/C=US/ST=State/L=City/O=TOM/OU=IT/CN=localhost"

# Generate self-signed certificate
openssl x509 -req -days 365 -in server.csr -signkey server.key -out server.crt

# Clean up CSR file
rm server.csr

# Set appropriate permissions
chmod 600 server.key
chmod 644 server.crt

echo "SSL certificates generated successfully:"
echo "  - server.key (private key)"
echo "  - server.crt (certificate)"
echo ""
echo "These certificates are valid for 365 days and are intended for local network use only."
