# Docker Compose Override for Development
# This file disables application containers during local development
# Only infrastructure services (postgres, redis) will run in Docker
# All application services run locally in VS Code with debugging enabled

version: "3.9"

services:
  # Disable all application services for local development
  # These services will run locally in VS Code instead
  
  # API service - disabled for local development
  api:
    deploy:
      replicas: 0
    
  # Python microservices - disabled for local development  
  logging:
    deploy:
      replicas: 0
      
  settings:
    deploy:
      replicas: 0
      
  file-parsing:
    deploy:
      replicas: 0
      
  rules-engine:
    deploy:
      replicas: 0
      
  robotic-integration:
    deploy:
      replicas: 0
      
  # Frontend services - disabled for local development
  main-ui:
    deploy:
      replicas: 0
      
  board-display:
    deploy:
      replicas: 0
      
  # Nginx load balancer - disabled for local development
  nginx:
    deploy:
      replicas: 0

# Infrastructure services (postgres, redis) remain enabled
# and will be started with: docker-compose up postgres redis
