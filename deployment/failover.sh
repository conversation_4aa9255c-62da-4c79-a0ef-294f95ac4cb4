#!/bin/bash

# Truss Robotic Management System High Availability Failover Script
# Handles automatic failover to standby systems

set -e

# Configuration
PRIMARY_HOST="*********"
STANDBY_HOST="*********"
VIP="**********"  # Virtual IP for alias
INTERFACE="eth0"
LOG_FILE="./failover.log"
LOCK_FILE="/tmp/failover.lock"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

# Check if script is already running
check_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid
        pid=$(cat "$LOCK_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            error "Failover script is already running (PID: $pid)"
            exit 1
        else
            warn "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

# Cleanup function
cleanup() {
    rm -f "$LOCK_FILE"
}
trap cleanup EXIT

# Get current host IP
get_current_host() {
    hostname -I | awk '{print $1}'
}

# Check if current host has VIP
has_vip() {
    ip addr show "$INTERFACE" | grep -q "$VIP"
}

# Add VIP to interface
add_vip() {
    log "Adding VIP $VIP to interface $INTERFACE"
    if ip addr add "$VIP/24" dev "$INTERFACE"; then
        success "VIP added successfully"
        return 0
    else
        error "Failed to add VIP"
        return 1
    fi
}

# Remove VIP from interface
remove_vip() {
    log "Removing VIP $VIP from interface $INTERFACE"
    if ip addr del "$VIP/24" dev "$INTERFACE" 2>/dev/null; then
        success "VIP removed successfully"
        return 0
    else
        warn "VIP was not present or failed to remove"
        return 1
    fi
}

# Check if remote host is reachable
check_host_reachable() {
    local host=$1
    local timeout=${2:-5}

    if ping -c 1 -W "$timeout" "$host" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# Check if services are healthy on a host
check_services_healthy() {
    local host=$1

    # Check if Docker services are running
    if ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$host" \
       "docker-compose -f /opt/truss/deployment/docker-compose.yml ps | grep -q 'Up'" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# Promote standby to primary
promote_to_primary() {
    log "Promoting standby to primary..."

    # Add VIP
    if ! add_vip; then
        error "Failed to add VIP during promotion"
        return 1
    fi

    # Start services if not running
    log "Starting services..."
    if docker-compose -f docker-compose.yml up -d; then
        success "Services started successfully"
    else
        error "Failed to start services"
        remove_vip
        return 1
    fi

    # Wait for services to be ready
    sleep 30

    # Verify services are healthy
    if ./health-check.sh check; then
        success "Promotion to primary completed successfully"
        return 0
    else
        error "Services are not healthy after promotion"
        return 1
    fi
}

# Demote primary to standby
demote_to_standby() {
    log "Demoting primary to standby..."

    # Remove VIP
    remove_vip

    # Stop services gracefully
    log "Stopping services..."
    docker-compose -f docker-compose.yml down

    success "Demotion to standby completed"
}

# Check cluster status
check_cluster_status() {
    local current_host
    current_host=$(get_current_host)

    log "Checking cluster status..."
    log "Current host: $current_host"
    log "Primary host: $PRIMARY_HOST"
    log "Standby host: $STANDBY_HOST"

    # Check if we have VIP
    if has_vip; then
        log "This host has VIP - acting as PRIMARY"
        return 0
    else
        log "This host does not have VIP - acting as STANDBY"
        return 1
    fi
}

# Monitor and failover logic
monitor_and_failover() {
    local check_interval=${1:-30}  # Default 30 seconds
    local failure_threshold=${2:-3}  # Default 3 consecutive failures
    local failure_count=0

    log "Starting failover monitoring (interval: ${check_interval}s, threshold: $failure_threshold)"

    while true; do
        local current_host
        current_host=$(get_current_host)

        if [ "$current_host" = "$STANDBY_HOST" ]; then
            # We are standby - monitor primary
            if check_host_reachable "$PRIMARY_HOST" && check_services_healthy "$PRIMARY_HOST"; then
                # Primary is healthy
                if [ $failure_count -gt 0 ]; then
                    log "Primary recovered, resetting failure count"
                    failure_count=0
                fi
            else
                # Primary is not healthy
                ((failure_count++))
                warn "Primary health check failed (attempt $failure_count/$failure_threshold)"

                if [ $failure_count -ge $failure_threshold ]; then
                    error "Primary has failed $failure_threshold consecutive checks - initiating failover"

                    if promote_to_primary; then
                        success "Failover completed successfully"
                        # Send notification
                        echo "Failover completed: $STANDBY_HOST promoted to primary" | \
                            mail -s "Truss System Failover" <EMAIL> 2>/dev/null || true
                        break
                    else
                        error "Failover failed"
                        failure_count=0  # Reset and continue monitoring
                    fi
                fi
            fi
        elif [ "$current_host" = "$PRIMARY_HOST" ]; then
            # We are primary - just monitor our own health
            if ! ./health-check.sh check &> /dev/null; then
                warn "Primary health check failed"
            fi
        fi

        sleep "$check_interval"
    done
}

# Manual failover
manual_failover() {
    local direction=$1

    case $direction in
        "to-standby")
            if check_cluster_status; then
                log "Manual failover: demoting primary to standby"
                demote_to_standby
            else
                warn "This host is already standby"
            fi
            ;;
        "to-primary")
            if ! check_cluster_status; then
                log "Manual failover: promoting standby to primary"
                promote_to_primary
            else
                warn "This host is already primary"
            fi
            ;;
        *)
            error "Invalid failover direction: $direction"
            exit 1
            ;;
    esac
}

# Status check
status() {
    local current_host
    current_host=$(get_current_host)

    echo "=== Truss System HA Status ==="
    echo "Current host: $current_host"
    echo "Primary host: $PRIMARY_HOST"
    echo "Standby host: $STANDBY_HOST"
    echo "Virtual IP: $VIP"
    echo ""

    if has_vip; then
        echo "Role: PRIMARY (has VIP)"
    else
        echo "Role: STANDBY (no VIP)"
    fi

    echo ""
    echo "Primary reachable: $(check_host_reachable "$PRIMARY_HOST" && echo "YES" || echo "NO")"
    echo "Standby reachable: $(check_host_reachable "$STANDBY_HOST" && echo "YES" || echo "NO")"

    echo ""
    echo "Services status:"
    ./health-check.sh check || true
}

# Main script logic
check_lock

case "${1:-status}" in
    "monitor")
        monitor_and_failover "$2" "$3"
        ;;
    "failover")
        manual_failover "$2"
        ;;
    "status")
        status
        ;;
    "promote")
        promote_to_primary
        ;;
    "demote")
        demote_to_standby
        ;;
    *)
        echo "Usage: $0 {monitor|failover|status|promote|demote} [options]"
        echo "  monitor [interval] [threshold]  - Start automatic failover monitoring"
        echo "  failover {to-primary|to-standby} - Manual failover"
        echo "  status                          - Show cluster status"
        echo "  promote                         - Promote this host to primary"
        echo "  demote                          - Demote this host to standby"
        exit 1
        ;;
esac