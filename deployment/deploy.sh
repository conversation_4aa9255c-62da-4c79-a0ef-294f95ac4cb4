#!/bin/bash

# Truss Robotic Management System Deployment Script
# Supports blue-green deployment for zero-downtime updates

set -e

# Configuration
COMPOSE_FILE="docker-compose.yml"
COMPOSE_PROD_FILE="docker-compose.prod.yml"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
        exit 1
    fi

    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi

    log "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."

    mkdir -p "$BACKUP_DIR"
    BACKUP_NAME="backup_$(date +'%Y%m%d_%H%M%S')"

    # Backup database
    docker-compose exec -T postgres pg_dump -U user truss > "$BACKUP_DIR/${BACKUP_NAME}_db.sql"

    # Backup Redis data
    docker-compose exec -T redis redis-cli BGSAVE
    docker cp $(docker-compose ps -q redis):/data/dump.rdb "$BACKUP_DIR/${BACKUP_NAME}_redis.rdb"

    log "Backup created: $BACKUP_NAME"
}

# Health check function
health_check() {
    local service=$1
    local max_attempts=30
    local attempt=1

    log "Performing health check for $service..."

    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T "$service" curl -f http://localhost:8000/health &> /dev/null; then
            log "$service is healthy"
            return 0
        fi

        warn "Health check attempt $attempt/$max_attempts failed for $service"
        sleep 10
        ((attempt++))
    done

    error "Health check failed for $service after $max_attempts attempts"
    return 1
}

# Deploy function
deploy() {
    local deployment_type=${1:-"standard"}

    log "Starting deployment (type: $deployment_type)..."

    case $deployment_type in
        "blue-green")
            deploy_blue_green
            ;;
        "rolling")
            deploy_rolling
            ;;
        *)
            deploy_standard
            ;;
    esac
}

# Standard deployment
deploy_standard() {
    log "Performing standard deployment..."

    # Pull latest images
    docker-compose pull

    # Build services
    docker-compose build --no-cache

    # Stop services
    docker-compose down

    # Start services
    docker-compose up -d

    # Wait for services to be ready
    sleep 30

    # Health checks
    for service in logging settings file-parsing rules-engine robotic-integration; do
        health_check "$service"
    done

    log "Standard deployment completed successfully"
}

# Blue-green deployment
deploy_blue_green() {
    log "Performing blue-green deployment..."

    # This is a simplified blue-green deployment
    # In production, you would have separate environments

    # Create green environment
    docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_PROD_FILE" -p truss-green up -d --build

    # Health check green environment
    sleep 60

    # Switch traffic (this would involve updating load balancer configuration)
    log "Switching traffic to green environment..."

    # Stop blue environment
    docker-compose -p truss-blue down

    # Rename green to blue
    docker-compose -p truss-green stop
    docker-compose -p truss-blue up -d

    log "Blue-green deployment completed successfully"
}

# Rolling deployment
deploy_rolling() {
    log "Performing rolling deployment..."

    # Update services one by one
    for service in logging settings file-parsing rules-engine robotic-integration api; do
        log "Updating service: $service"

        docker-compose up -d --no-deps --build "$service"
        sleep 30
        health_check "$service"
    done

    log "Rolling deployment completed successfully"
}

# Rollback function
rollback() {
    local backup_name=$1

    if [ -z "$backup_name" ]; then
        error "Backup name is required for rollback"
        exit 1
    fi

    log "Rolling back to backup: $backup_name..."

    # Stop services
    docker-compose down

    # Restore database
    if [ -f "$BACKUP_DIR/${backup_name}_db.sql" ]; then
        docker-compose up -d postgres
        sleep 30
        docker-compose exec -T postgres psql -U user -d truss < "$BACKUP_DIR/${backup_name}_db.sql"
    fi

    # Restore Redis
    if [ -f "$BACKUP_DIR/${backup_name}_redis.rdb" ]; then
        docker cp "$BACKUP_DIR/${backup_name}_redis.rdb" $(docker-compose ps -q redis):/data/dump.rdb
        docker-compose restart redis
    fi

    # Start all services
    docker-compose up -d

    log "Rollback completed successfully"
}

# Main script logic
case "${1:-deploy}" in
    "deploy")
        check_prerequisites
        create_backup
        deploy "${2:-standard}"
        ;;
    "rollback")
        rollback "$2"
        ;;
    "health-check")
        health_check "$2"
        ;;
    "backup")
        create_backup
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health-check|backup} [options]"
        echo "  deploy [standard|blue-green|rolling] - Deploy the application"
        echo "  rollback <backup_name>               - Rollback to a specific backup"
        echo "  health-check <service_name>          - Check health of a service"
        echo "  backup                               - Create a backup"
        exit 1
        ;;
esac