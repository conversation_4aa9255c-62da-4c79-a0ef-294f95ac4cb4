{"version": "2.0.0", "tasks": [{"label": "🐳 Start Infrastructure (Postgres + Redis)", "type": "shell", "command": "docker", "args": ["compose", "up", "-d", "postgres", "redis"], "options": {"cwd": "${workspaceFolder}"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "infrastructure"}, "problemMatcher": []}, {"label": "🛑 Stop Infrastructure", "type": "shell", "command": "docker", "args": ["compose", "down"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "infrastructure"}, "problemMatcher": []}, {"label": "🚀 Deploy Production Stack", "type": "shell", "command": "docker", "args": ["compose", "-f", "docker-compose.yml", "-f", "docker-compose.prod.yml", "up", "-d", "--build"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "production"}, "problemMatcher": []}, {"label": "🛑 Stop Production Stack", "type": "shell", "command": "docker", "args": ["compose", "-f", "docker-compose.yml", "-f", "docker-compose.prod.yml", "down"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "production"}, "problemMatcher": []}, {"label": "📊 View Container Status", "type": "shell", "command": "docker", "args": ["ps", "-a"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "monitoring"}, "problemMatcher": []}, {"label": "📋 View Container Logs", "type": "shell", "command": "docker", "args": ["compose", "logs", "-f"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "monitoring"}, "problemMatcher": []}]}