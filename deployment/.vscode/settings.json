{
  // File Associations
  "files.associations": {
    "docker-compose*.yml": "dockercompose",
    "Dockerfile*": "dockerfile",
    "*.sh": "shellscript",
    "*.conf": "nginx",
    "nginx.conf": "nginx"
  },

  // YAML Configuration
  "[yaml]": {
    "editor.defaultFormatter": "redhat.vscode-yaml",
    "editor.formatOnSave": true,
    "editor.insertSpaces": true,
    "editor.tabSize": 2
  },

  // Shell Script Configuration
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format",
    "editor.formatOnSave": true
  },

  // Docker Configuration
  "docker.defaultRegistryPath": "",
  "docker.showStartPage": false,

  // Search Configuration
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true
  },

  // Terminal Configuration
  "terminal.integrated.defaultProfile.linux": "bash",

  // Editor Configuration
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": true,

  // Auto Save Configuration
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000
}
