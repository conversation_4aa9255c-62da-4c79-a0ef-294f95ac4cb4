{"version": "0.2.0", "configurations": [{"name": "🐳 Start Infrastructure (Postgres + Redis)", "type": "node", "request": "launch", "runtimeExecutable": "docker", "runtimeArgs": ["compose", "up", "-d", "postgres", "redis"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "presentation": {"hidden": false, "group": "infrastructure", "order": 1}}, {"name": "🛑 Stop Infrastructure", "type": "node", "request": "launch", "runtimeExecutable": "docker", "runtimeArgs": ["compose", "down"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "presentation": {"hidden": false, "group": "infrastructure", "order": 2}}, {"name": "🚀 Deploy Production Stack", "type": "node", "request": "launch", "runtimeExecutable": "docker", "runtimeArgs": ["compose", "-f", "docker-compose.yml", "-f", "docker-compose.prod.yml", "up", "-d", "--build"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "presentation": {"hidden": false, "group": "production", "order": 1}}, {"name": "🛑 Stop Production Stack", "type": "node", "request": "launch", "runtimeExecutable": "docker", "runtimeArgs": ["compose", "-f", "docker-compose.yml", "-f", "docker-compose.prod.yml", "down"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "presentation": {"hidden": false, "group": "production", "order": 2}}]}