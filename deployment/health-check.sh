#!/bin/bash

# Truss Robotic Management System Health Check Script
# Monitors all services and provides detailed health status

set -e

# Configuration
SERVICES=("postgres" "redis" "nginx" "api" "logging" "settings" "file-parsing" "rules-engine" "robotic-integration" "main-ui" "board-display")
LOG_FILE="./health-check.log"
ALERT_WEBHOOK=""  # Set this to your alerting webhook URL

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

# Check if Docker Compose is running
check_docker_compose() {
    if ! docker-compose ps &> /dev/null; then
        error "Docker Compose is not running or accessible"
        return 1
    fi
    return 0
}

# Check individual service health
check_service_health() {
    local service=$1
    local container_id
    local status

    container_id=$(docker-compose ps -q "$service" 2>/dev/null)

    if [ -z "$container_id" ]; then
        error "Service $service: Container not found"
        return 1
    fi

    status=$(docker inspect --format='{{.State.Status}}' "$container_id" 2>/dev/null)

    if [ "$status" != "running" ]; then
        error "Service $service: Container status is $status"
        return 1
    fi

    # Service-specific health checks
    case $service in
        "postgres")
            if docker-compose exec -T postgres pg_isready -U user -d truss &> /dev/null; then
                success "Service $service: Database is ready"
                return 0
            else
                error "Service $service: Database is not ready"
                return 1
            fi
            ;;
        "redis")
            if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
                success "Service $service: Redis is responding"
                return 0
            else
                error "Service $service: Redis is not responding"
                return 1
            fi
            ;;
        "nginx")
            if docker-compose exec -T nginx wget --quiet --tries=1 --spider http://localhost/health &> /dev/null; then
                success "Service $service: Nginx is serving requests"
                return 0
            else
                error "Service $service: Nginx is not responding"
                return 1
            fi
            ;;
        "api"|"logging"|"settings"|"file-parsing"|"rules-engine"|"robotic-integration")
            local port=8000
            if [ "$service" = "api" ]; then
                port=4000
            fi

            if docker-compose exec -T "$service" curl -f "http://localhost:$port/health" &> /dev/null; then
                success "Service $service: Health endpoint is responding"
                return 0
            else
                error "Service $service: Health endpoint is not responding"
                return 1
            fi
            ;;
        "main-ui"|"board-display")
            if docker-compose exec -T "$service" curl -f http://localhost:3000 &> /dev/null; then
                success "Service $service: Frontend is serving"
                return 0
            else
                error "Service $service: Frontend is not responding"
                return 1
            fi
            ;;
        *)
            warn "Service $service: No specific health check defined"
            return 0
            ;;
    esac
}

# Check resource usage
check_resource_usage() {
    log "Checking resource usage..."

    # Check disk usage
    local disk_usage
    disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')

    if [ "$disk_usage" -gt 90 ]; then
        error "Disk usage is critical: ${disk_usage}%"
    elif [ "$disk_usage" -gt 80 ]; then
        warn "Disk usage is high: ${disk_usage}%"
    else
        success "Disk usage is normal: ${disk_usage}%"
    fi

    # Check memory usage
    local memory_usage
    memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')

    if [ "$memory_usage" -gt 90 ]; then
        error "Memory usage is critical: ${memory_usage}%"
    elif [ "$memory_usage" -gt 80 ]; then
        warn "Memory usage is high: ${memory_usage}%"
    else
        success "Memory usage is normal: ${memory_usage}%"
    fi
}

# Check database connections
check_database_connections() {
    log "Checking database connections..."

    local active_connections
    active_connections=$(docker-compose exec -T postgres psql -U user -d truss -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs)

    if [ -n "$active_connections" ]; then
        if [ "$active_connections" -gt 50 ]; then
            warn "High number of active database connections: $active_connections"
        else
            success "Database connections are normal: $active_connections active"
        fi
    else
        error "Could not retrieve database connection count"
    fi
}

# Check Redis memory usage
check_redis_memory() {
    log "Checking Redis memory usage..."

    local redis_memory
    redis_memory=$(docker-compose exec -T redis redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')

    if [ -n "$redis_memory" ]; then
        success "Redis memory usage: $redis_memory"
    else
        error "Could not retrieve Redis memory usage"
    fi
}

# Send alert if configured
send_alert() {
    local message=$1

    if [ -n "$ALERT_WEBHOOK" ]; then
        curl -X POST -H "Content-Type: application/json" \
             -d "{\"text\":\"Truss System Alert: $message\"}" \
             "$ALERT_WEBHOOK" &> /dev/null || warn "Failed to send alert"
    fi
}

# Main health check function
run_health_check() {
    local failed_services=()
    local total_services=${#SERVICES[@]}
    local healthy_services=0

    log "Starting comprehensive health check..."
    log "Checking $total_services services..."

    # Check Docker Compose
    if ! check_docker_compose; then
        error "Docker Compose check failed"
        send_alert "Docker Compose is not accessible"
        exit 1
    fi

    # Check each service
    for service in "${SERVICES[@]}"; do
        if check_service_health "$service"; then
            ((healthy_services++))
        else
            failed_services+=("$service")
        fi
    done

    # Check system resources
    check_resource_usage
    check_database_connections
    check_redis_memory

    # Summary
    log "Health check summary:"
    log "Healthy services: $healthy_services/$total_services"

    if [ ${#failed_services[@]} -eq 0 ]; then
        success "All services are healthy!"
        exit 0
    else
        error "Failed services: ${failed_services[*]}"
        send_alert "Services failing health check: ${failed_services[*]}"
        exit 1
    fi
}

# Continuous monitoring mode
monitor() {
    local interval=${1:-60}  # Default 60 seconds

    log "Starting continuous monitoring (interval: ${interval}s)..."

    while true; do
        run_health_check
        sleep "$interval"
    done
}

# Main script logic
case "${1:-check}" in
    "check")
        run_health_check
        ;;
    "monitor")
        monitor "$2"
        ;;
    "service")
        if [ -z "$2" ]; then
            error "Service name is required"
            exit 1
        fi
        check_service_health "$2"
        ;;
    *)
        echo "Usage: $0 {check|monitor|service} [options]"
        echo "  check                    - Run one-time health check"
        echo "  monitor [interval]       - Run continuous monitoring (default: 60s)"
        echo "  service <service_name>   - Check specific service"
        exit 1
        ;;
esac