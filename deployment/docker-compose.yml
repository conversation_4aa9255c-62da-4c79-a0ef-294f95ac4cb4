version: "3.9"

services:
  # Database - PostgreSQL for data persistence
  postgres:
    image: postgres:15
    container_name: tom-postgres-dev
    environment:
      POSTGRES_DB: tom
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d tom"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # Redis - For caching and pub/sub messaging
  redis:
    image: redis:7-alpine
    container_name: tom-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  tom-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16