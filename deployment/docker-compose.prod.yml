version: "3.9"

services:
  # Load Balancer - Only for production
  nginx:
    image: nginx:alpine
    container_name: tom-nginx-prod
    network_mode: "host"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./server.crt:/etc/nginx/server.crt:ro
      - ./server.key:/etc/nginx/server.key:ro
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Node.js API/WebSocket Server - Production
  api:
    build:
      context: ../backend/api
      dockerfile: Dockerfile
    container_name: tom-api-prod
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
        reservations:
          cpus: "0.5"
          memory: 1g
    environment:
      - DATABASE_URL=****************************************/tom
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=production
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      logging:
        condition: service_started
      settings:
        condition: service_started
      file-parsing:
        condition: service_started
      rules-engine:
        condition: service_started
      robotic-integration:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # Logging Microservice - Production
  logging:
    build:
      context: ../backend/logging
      dockerfile: Dockerfile
    container_name: tom-logging-prod
    environment:
      - DATABASE_URL=****************************************/tom
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # Settings Microservice - Production
  settings:
    build:
      context: ../backend/settings
      dockerfile: Dockerfile
    container_name: tom-settings-prod
    environment:
      - DATABASE_URL=****************************************/tom
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      logging:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # File Parsing Microservice - Production
  file-parsing:
    build:
      context: ../backend/file-parsing
      dockerfile: Dockerfile
    container_name: tom-file-parsing-prod
    environment:
      - DATABASE_URL=****************************************/tom
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      logging:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # Rules Engine Microservice - Production
  rules-engine:
    build:
      context: ../backend/rules-engine
      dockerfile: Dockerfile
    container_name: tom-rules-engine-prod
    environment:
      - DATABASE_URL=****************************************/tom
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      logging:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # Robotic Integration Microservice - Production
  robotic-integration:
    build:
      context: ../backend/robotic-integration
      dockerfile: Dockerfile
    container_name: tom-robotic-integration-prod
    environment:
      - DATABASE_URL=****************************************/tom
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      logging:
        condition: service_started
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - tom-network

  # Main UI Frontend - Production
  main-ui:
    build:
      context: ../frontend/main-ui
      dockerfile: Dockerfile
    container_name: tom-main-ui-prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - API_URL=https://localhost
    depends_on:
      - nginx
    networks:
      - tom-network

  # Board Display Frontend - Production
  board-display:
    build:
      context: ../frontend/board-display
      dockerfile: Dockerfile
    container_name: tom-board-display-prod
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - API_URL=https://localhost
    depends_on:
      - nginx
    networks:
      - tom-network

networks:
  tom-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
