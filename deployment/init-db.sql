-- Truss Robotic Management System Database Initialization
-- This script sets up the initial database structure and configuration

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create custom types
CREATE TYPE entity_type AS ENUM ('batch');

-- Create sequences for auto-incrementing IDs
CREATE SEQUENCE IF NOT EXISTS tenant_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS user_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS log_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS setting_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS tag_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS tags_relation_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS truss_file_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS batch_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS truss_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS board_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS plate_id_seq START 1;
CREATE SEQUENCE IF NOT EXISTS truss_build_id_seq START 1;

-- Create database user for application (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'truss_app') THEN
        CREATE ROLE truss_app WITH LOGIN PASSWORD 'truss_app_password';
    END IF;
END
$$;

-- Grant necessary permissions
GRANT CONNECT ON DATABASE truss TO truss_app;
GRANT USAGE ON SCHEMA public TO truss_app;
GRANT CREATE ON SCHEMA public TO truss_app;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO truss_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO truss_app;

-- Grant table permissions (will be applied to future tables)
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO truss_app;

-- Create initial tenant for development/testing
INSERT INTO tenants (id, name) VALUES (1, 'Default Tenant') ON CONFLICT (id) DO NOTHING;

-- Create default admin user
INSERT INTO users (id, tenant_id, username, pin, role) 
VALUES (1, 1, 'admin', '0000', 'Super Admin') 
ON CONFLICT (id) DO NOTHING;

-- Create default settings
INSERT INTO settings (id, tenant_id, key, value, version) 
VALUES 
    (1, 1, 'system.theme', '{"primary_color": "#2563eb", "secondary_color": "#64748b"}', 1),
    (2, 1, 'system.timezone', '{"timezone": "UTC", "format": "24h"}', 1),
    (3, 1, 'rules.default', '{"manual_plate_width_threshold": 3.0, "manual_plate_width_min": 2.0}', 1)
ON CONFLICT (id) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_tenant_username ON users(tenant_id, username);
CREATE INDEX IF NOT EXISTS idx_logs_tenant_timestamp ON logs(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_settings_tenant_key ON settings(tenant_id, key);
CREATE INDEX IF NOT EXISTS idx_truss_files_tenant_created ON truss_files(tenant_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_batches_tenant_status ON batches(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_trusses_batch_tenant ON trusses(batch_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_boards_truss_tenant ON boards(truss_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_plates_truss_tenant ON plates(truss_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_tags_relations_entity ON tags_relations(entity_type, type_id);
CREATE INDEX IF NOT EXISTS idx_truss_builds_tenant_timestamp ON truss_builds(tenant_id, build_timestamp DESC);

-- Create partial indexes for active records
CREATE INDEX IF NOT EXISTS idx_batches_active ON batches(tenant_id, id) WHERE status IN ('active', 'processing');
CREATE INDEX IF NOT EXISTS idx_boards_manual ON boards(truss_id) WHERE manual = true;
CREATE INDEX IF NOT EXISTS idx_plates_manual ON plates(truss_id) WHERE manual = true;

-- Create function for updating timestamps
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modified_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function for generating UUIDs
CREATE OR REPLACE FUNCTION generate_uuid()
RETURNS TEXT AS $$
BEGIN
    RETURN uuid_generate_v4()::TEXT;
END;
$$ language 'plpgsql';

-- Create function for tenant isolation check
CREATE OR REPLACE FUNCTION check_tenant_isolation()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure tenant_id is always set and valid
    IF NEW.tenant_id IS NULL THEN
        RAISE EXCEPTION 'tenant_id cannot be null';
    END IF;
    
    -- Additional tenant validation can be added here
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Set up row-level security (RLS) for tenant isolation
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE truss_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE trusses ENABLE ROW LEVEL SECURITY;
ALTER TABLE boards ENABLE ROW LEVEL SECURITY;
ALTER TABLE plates ENABLE ROW LEVEL SECURITY;
ALTER TABLE truss_builds ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (these will be activated when needed)
-- Note: In development, RLS might be disabled for easier testing

-- Create materialized view for dashboard statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS dashboard_stats AS
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    COUNT(DISTINCT b.id) as total_batches,
    COUNT(DISTINCT tr.id) as total_trusses,
    COUNT(DISTINCT bo.id) as total_boards,
    COUNT(DISTINCT p.id) as total_plates,
    COUNT(DISTINCT CASE WHEN b.status = 'active' THEN b.id END) as active_batches,
    COUNT(DISTINCT CASE WHEN bo.manual = true THEN bo.id END) as manual_boards,
    COUNT(DISTINCT CASE WHEN p.manual = true THEN p.id END) as manual_plates
FROM tenants t
LEFT JOIN batches b ON t.id = b.tenant_id
LEFT JOIN trusses tr ON b.id = tr.batch_id
LEFT JOIN boards bo ON tr.id = bo.truss_id
LEFT JOIN plates p ON tr.id = p.truss_id
GROUP BY t.id, t.name;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_dashboard_stats_tenant ON dashboard_stats(tenant_id);

-- Create function to refresh dashboard stats
CREATE OR REPLACE FUNCTION refresh_dashboard_stats()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY dashboard_stats;
END;
$$ language 'plpgsql';

-- Set up automatic statistics refresh (can be called by cron or application)
-- This would typically be set up as a scheduled job

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO logs (tenant_id, action_type, target_data, timestamp)
        VALUES (NEW.tenant_id, TG_OP || '_' || TG_TABLE_NAME, row_to_json(NEW), CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO logs (tenant_id, action_type, target_data, timestamp)
        VALUES (NEW.tenant_id, TG_OP || '_' || TG_TABLE_NAME, 
                json_build_object('old', row_to_json(OLD), 'new', row_to_json(NEW)), 
                CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO logs (tenant_id, action_type, target_data, timestamp)
        VALUES (OLD.tenant_id, TG_OP || '_' || TG_TABLE_NAME, row_to_json(OLD), CURRENT_TIMESTAMP);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Note: Audit triggers can be enabled on specific tables as needed
-- Example: CREATE TRIGGER audit_batches AFTER INSERT OR UPDATE OR DELETE ON batches FOR EACH ROW EXECUTE FUNCTION audit_trigger();

-- Create backup and maintenance functions
CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM logs WHERE timestamp < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION update_modified_column() TO truss_app;
GRANT EXECUTE ON FUNCTION generate_uuid() TO truss_app;
GRANT EXECUTE ON FUNCTION refresh_dashboard_stats() TO truss_app;
GRANT EXECUTE ON FUNCTION cleanup_old_logs(INTEGER) TO truss_app;

-- Final setup message
DO $$
BEGIN
    RAISE NOTICE 'Truss Robotic Management System database initialization completed successfully';
    RAISE NOTICE 'Default tenant created with ID: 1';
    RAISE NOTICE 'Default admin user created: admin/0000';
    RAISE NOTICE 'Database is ready for application deployment';
END
$$;
