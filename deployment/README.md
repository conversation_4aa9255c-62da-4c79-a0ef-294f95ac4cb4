# TOM Deployment Configuration

This directory contains the Docker Compose configurations for the TOM (Task Orchestration Manager) project.

## 📁 File Structure

```
deployment/
├── docker-compose.yml           # Infrastructure services (PostgreSQL, Redis)
├── docker-compose.prod.yml      # Production application stack
├── docker-compose.override.yml  # Development overrides (disables app containers)
├── nginx.conf                   # Nginx configuration for production
├── init-db.sql                  # Database initialization script
└── README.md                    # This file
```

## 🐳 Docker Compose Files

### **1. `docker-compose.yml` (Base Configuration)**

- **Purpose**: Infrastructure services for development
- **Contains**: PostgreSQL (port 5434), Redis (port 6379)
- **Usage**: Provides database and cache for local development
- **Networks**: `tom-network` bridge network
- **Volumes**: Persistent storage for PostgreSQL and Redis data

### **2. `docker-compose.prod.yml` (Production Stack)**

- **Purpose**: Complete application stack for production deployment
- **Contains**: All application services (API, microservices, frontend apps)
- **Usage**: Production deployment with full containerization
- **Dependencies**: Requires base configuration from `docker-compose.yml`
- **Features**: Load balancer (Nginx), health checks, resource limits

### **3. `docker-compose.override.yml` (Development Overrides)**

- **Purpose**: Disables application containers during local development
- **Contains**: Service overrides with `replicas: 0` for all app services
- **Usage**: Automatically applied in development to prevent container conflicts
- **Benefit**: Ensures only infrastructure runs while apps run locally in VS Code

## 🚀 Usage Scenarios

### **Development (Local VS Code Debugging)**

```bash
# Start only infrastructure services
cd deployment
docker compose up postgres redis -d

# The override file automatically disables application containers
# Run application services locally in VS Code for debugging
```

### **Production Deployment**

```bash
# Deploy complete application stack
cd deployment
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
```

### **Infrastructure Management**

```bash
# View running containers
docker compose ps

# View logs
docker compose logs -f postgres redis

# Stop infrastructure
docker compose down

# Clean up (removes volumes - WARNING: deletes data)
docker compose down -v
```

## 🌐 Service Ports

### **Infrastructure Services**

| Service    | Port | Purpose                    |
| ---------- | ---- | -------------------------- |
| PostgreSQL | 5442 | Database (external access) |
| Redis      | 6389 | Cache/Pub-Sub              |

### **Application Services (Production Only)**

| Service               | Port   | Purpose                |
| --------------------- | ------ | ---------------------- |
| Nginx (Load Balancer) | 80/443 | HTTP/HTTPS entry point |
| API Server            | 4010   | REST API/WebSocket     |
| Main UI               | 3010   | React frontend         |
| Board Display         | 3011   | Display interface      |
| Logging Service       | 8011   | Logging microservice   |
| Settings Service      | 8012   | Settings microservice  |
| File Parsing          | 8013   | File processing        |
| Rules Engine          | 8014   | Business rules         |
| Robotic Integration   | 8015   | Hardware integration   |

## 🔧 Environment Variables

### **Database Configuration**

```env
POSTGRES_DB=tom
POSTGRES_USER=user
POSTGRES_PASSWORD=password
```

### **Application Configuration**

```env
DATABASE_URL=****************************************/tom
REDIS_URL=redis://redis:6379
NODE_ENV=production
```

## 🛡️ Security Considerations

### **Development**

- Database exposed on non-standard port (5434) to avoid conflicts
- Default credentials (change for production)
- No SSL/TLS encryption

### **Production**

- Nginx handles SSL termination
- Services communicate via internal network
- Health checks for service monitoring
- Resource limits to prevent resource exhaustion

## 📊 Monitoring & Health Checks

All production services include health checks:

- **PostgreSQL**: `pg_isready` command
- **Redis**: `redis-cli ping` command
- **API Services**: HTTP health endpoints
- **Frontend Apps**: HTTP availability checks

## 🔄 Data Persistence

### **Volumes**

- `postgres_data`: PostgreSQL database files
- `redis_data`: Redis persistence files

### **Backup Strategy**

```bash
# Backup PostgreSQL
docker exec deployment_postgres_1 pg_dump -U user tom > backup.sql

# Restore PostgreSQL
docker exec -i deployment_postgres_1 psql -U user tom < backup.sql
```

## 🐛 Troubleshooting

### **Common Issues**

1. **Port Conflicts**

   ```bash
   # Check what's using the ports
   lsof -i :5442
   lsof -i :6389
   ```

2. **Container Won't Start**

   ```bash
   # Check container logs
   docker compose logs postgres
   docker compose logs redis
   ```

3. **Database Connection Issues**

   ```bash
   # Test database connection
   docker exec -it deployment_postgres_1 psql -U user -d tom
   ```

4. **Volume Issues**
   ```bash
   # Remove volumes (WARNING: deletes data)
   docker compose down -v
   docker volume prune
   ```

### **Reset Environment**

```bash
# Complete reset (WARNING: deletes all data)
docker compose down -v
docker system prune -f
docker compose up postgres redis -d
```

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Redis Docker Image](https://hub.docker.com/_/redis)
- [Nginx Docker Image](https://hub.docker.com/_/nginx)
