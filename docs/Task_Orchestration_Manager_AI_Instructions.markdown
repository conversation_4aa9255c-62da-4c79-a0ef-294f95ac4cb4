# Instructions for Coding AI Agent: Task Orchestration Manager (TOM)

## 1. Overview

You are tasked with developing the **Task Orchestration Manager (TOM)**, a software solution for managing task batch creation, editing, scheduling, and real-time tracking in robotic manufacturing systems. The system supports multiple robotic systems (tracked via `system_id` in every database table), high availability (HA) with an optional hot standby PC, seamless updates, and real-time robotic integration via WebSockets. It handles various task file formats, stores files as BLOBs in PostgreSQL (running in a Docker container), provides a dynamic user interface, and ensures scalability for millions of boards/plates. Tags are normalized in a single `tags_relations` table with an `entity_type` enum. Board and plate data are stored in dedicated tables with detailed attributes, modification tracking, and build snapshots. All database column names use snake_case, with primary keys named `id` and foreign keys named descriptively (e.g., `truss_id`). The backend is implemented as multiple microservices (Node.js API/WebSocket server, Python microservices for file parsing, rules engine, settings, logging, robotic integration), each in its own Docker container, with Nginx load balancing. Shared code (e.g., health endpoints, logging middleware) is used across Python microservices. Development is progressive, implementing and testing each component in order: logging, base UI with login, settings, file parsing, rules engine, robotic integration. Each TOM manages multiple robotic systems via `system_id`, but multiple separate TOM apps can run on one computer, each with its own set of containers and a different IP address (via IP aliases on the same NIC), managing different sets of robotic systems. UI/API communication is over HTTPS (using self-signed certs for production, as systems are accessed by IP on local networks without internet); internal container communication is unencrypted HTTP. Each TOM instance is completely isolated, with no communication or awareness between instances; each has its own separate Postgres database container.

These instructions guide you through generating the application, including frontend, backend, database (using **TypeORM** for Node.js and **Alembic** for Python microservices), shared DTOs, load balancing, and deployment.

## 2. Technologies

- **Frontend**: SolidJS with SolidStart (SSR for static parts), IndexedDB for caching.
- **Backend**:
  - **API/WebSocket Server**: Node.js with NestJS, handling REST API and WebSocket channels (`/instance_<system_id>`).
  - **Microservices**: Python with FastAPI (file parsing, rules engine, settings, logging, robotic integration), each in a separate container.
- **Database**: PostgreSQL (`system_id` in every table, BLOBs for files, `tags_relations`, detailed board/plate attributes, modification tracking, build snapshots, snake_case columns, `id` primary keys), **Alembic** for Python microservices and **TypeORM** for Node.js API, running in a Docker container (separate instance per TOM).
- **Message Broker**: Redis with Pub/Sub and Sentinel, caching task/build data (separate per TOM).
- **Shared DTOs**: Python Pydantic models in `shared/models/`, TypeScript DTOs in `shared/ts/` (generated via `pydantic-to-typescript`).
- **Shared Code**: Python utilities in `shared/python/` for health endpoints, logging middleware, and database connections.
- **Load Balancer**: Nginx for distributing HTTP and WebSocket traffic, with sticky sessions for WebSocket; configured for HTTPS with self-signed certs.
- **Deployment**: Docker Compose (default) or Kubernetes (optional), blue-green deployment; multiple TOM instances via IP aliases and separate Compose projects.
- **Networking**: Alias IPs for multiple TOM instances on one NIC, manual IP switch for HA.
- **Other**: JSON-based rules engine (`json-rules-engine` for Python/Node.js), Nginx for serving frontend over HTTPS.
- **Security**: Self-signed certs for production HTTPS (no internet access); internal container traffic HTTP.

## 3. Architecture

The system runs in Docker containers (or Kubernetes), optimized for modest PCs (16 GB RAM, SSD).

### 3.1 Components

- **Frontend**:
  - **Main UI**: SolidJS app for batch management, rules, settings, history viewing (SSR for settings forms).
  - **Board Loading Display**: Separate SolidJS app with IndexedDB caching.
- **Backend**:
  - **API/WebSocket Server** (Node.js, NestJS):
    - REST API for CRUD operations, history retrieval, tag management.
    - WebSocket server with channels (`/instance_<system_id>`).
    - Communicates with Python microservices via HTTP or Redis Pub/Sub.
  - **Microservices** (Python, FastAPI):
    - **Logging**: Records errors and actions in PostgreSQL.
    - **Settings**: Manages settings in Redis/PostgreSQL.
    - **File Parsing**: Parses BLOBs, stores original/modified board/plate attributes.
    - **Rules Engine**: Applies JSON-based rules.
    - **Robotic Integration**: WebSocket communication, stores robot data.
    - Each microservice runs in its own container, uses shared code (`shared/python/`), and imports Pydantic models from `shared/models/`.
- **Load Balancer**:
  - Nginx in a Docker container, distributing HTTPS and WebSocket traffic to Node.js API instances with sticky sessions (IP hashing).
- **Database**:
  - PostgreSQL with `system_id` in every table, running in a Docker container (separate per TOM).
  - Tables: `systems`, `truss_files` (BLOBs), `batches`, `trusses`, `boards`, `plates`, `settings`, `logs`, `tags`, `tags_relations`, `truss_builds`.
  - Primary keys named `id`, foreign keys descriptive (e.g., `truss_id`).
  - Partitioned `boards`/`plates` by `batch_id`.
  - Managed with Alembic (Python) and TypeORM (Node.js).
- **Shared DTOs**:
  - Python Pydantic models in `shared/models/`.
  - TypeScript DTOs in `shared/ts/` (generated).
- **Shared Code**:
  - Python utilities in `shared/python/` for health endpoints, logging middleware, database connections.
- **Message Broker**:
  - Redis Pub/Sub for real-time updates, caching task/build data (separate per TOM).
- **Deployment**:
  - Docker Compose (default) or Kubernetes.
  - Blue-green deployment for zero-downtime updates.
- **HA**:
  - Hot standby: PostgreSQL streaming replication (includes BLOBs), Redis primary-replica, all services replicated.
  - Non-HA: Single PC with containerized PostgreSQL and backups.
- **Multiple TOM Instances**:
  - Run multiple separate TOM apps on one computer, each with own containers (including separate Postgres and Redis).
  - Use IP aliases on the NIC (e.g., `ip addr add *************/24 dev eth0` for second TOM).
  - Each TOM uses `network_mode: "host"` for Nginx to bind to specific IP for HTTPS.
  - Each TOM manages a different set of robotic systems via `system_id`.

### 3.2 Data Flow

1. **File Upload**: UI sends files to Nginx (HTTPS), which forwards to a Node.js API instance; API stores BLOBs in PostgreSQL and sends requests to File Parsing Service (HTTP internal).
2. **Parsing**: File Parsing Service retrieves BLOBs, stores original values in `boards`/`plates` (`original_*` columns), applies modifications, logs changes in `modifications`, caches in Redis.
3. **Batch Management**: UI manages batches via Nginx (HTTPS) and Node.js API, stored with `system_id`, tags linked via `tags_relations` (`entity_type = 'batch'`), updates broadcast via Redis Pub/Sub.
4. **Robotic Integration**: Robots connect via WebSocket through Nginx (HTTPS, sticky sessions), Node.js API queries Robotic Integration Service or Redis, stores build snapshots in `truss_builds`.
5. **Settings/Rules**: Settings in Redis/PostgreSQL, rules in JSON, propagated via Pub/Sub.
6. **Board Display**: Queries PostgreSQL or Redis via Nginx (HTTPS), caches in IndexedDB.
7. **History**: API retrieves original values, modification history, and build snapshots.

## 4. Features to Implement

- **Multi-System Support**: Systems identified by `system_id` (in every table), single WebSocket server with channels, alias IPs for multiple TOM instances.
- **Task File Loading and Parsing**: Parse `.tre`, `.asd`, `.smp2`, `.bch`, `.pcl`, store as `BYTEA` in `truss_files`, track original/modified board/plate attributes.
- **Batch and Task Management**: Create/edit batches (name, description, tags via `tags_relations`), tasks, boards/plates in dedicated tables.
- **Real-Time Robotic Integration**: WebSocket-based data requests and status updates, store robot data in `truss_builds`.
- **Rules Configuration**: JSON-based rules (e.g., manual plates if width > 3” or ≤ 2”).
- **Settings Management**: Dynamic UI, stored in Redis/PostgreSQL, import/export/versioning.
- **Board Loading Display**: System-specific and consolidated views, IndexedDB caching.
- **Authentication and Authorization**: PIN-based login, RBAC with roles (Super Admin, Site Admin, User, Viewer), JWT tokens stored in Redis.
- **Logging**: Log errors/actions in PostgreSQL.
- **Modification Tracking**: Store original values (`original_*`), modification history (`modifications`), build snapshots (`truss_builds`) for boards/plates.
- **Load Balancing**: Nginx distributes HTTPS/WebSocket traffic across backend instances, with sticky sessions for WebSocket.
- **High Availability**: Hot standby with PostgreSQL/Redis replication, non-HA with backups.
- **Automatic Updates**: Blue-green deployment via Docker Compose/Kubernetes.
- **Scalability**: Partitioned `boards`/`plates`, Redis caching.
- **Multiple TOM Instances**: Separate Compose projects with host network mode for Nginx, binding to different IP aliases.
- **Security**: HTTPS via self-signed certs for external traffic; HTTP for internal container communication.

## 5. Development Instructions

Follow these steps to generate the application, producing all code, configurations, and scripts. Use self-signed certs for HTTPS (generate with `openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout server.key -out server.crt`).

### 5.1 Project Structure

Create a monorepo:

```
tom-system/
├── frontend/
│   ├── main-ui/              # SolidJS main UI
│   └── board-display/        # SolidJS board loading display
├── backend/
│   ├── api/                  # Node.js NestJS API/WebSocket server
│   ├── file-parsing/         # Python FastAPI microservice
│   ├── rules-engine/         # Python FastAPI microservice
│   ├── settings/             # Python FastAPI microservice
│   ├── logging/              # Python FastAPI microservice
│   ├── robotic-integration/  # Python FastAPI microservice
├── database/
│   ├── migrations/           # Alembic migrations
│   └── models/               # SQLAlchemy models
│       ├── __init__.py
│       ├── system.py
│       ├── tag.py
│       ├── tags_relation.py
│       ├── truss_file.py
│       ├── batch.py
│       ├── truss.py
│       ├── board.py
│       ├── plate.py
│       ├── setting.py
│       ├── log.py
│       ├── truss_build.py
│       ├── user.py
├── shared/
│   ├── models/               # Pydantic models
│   │   ├── __init__.py
│   │   ├── system.py
│   │   ├── tag.py
│   │   ├── tags_relation.py
│   │   ├── truss_file.py
│   │   ├── batch.py
│   │   ├── truss.py
│   │   ├── board.py
│   │   ├── plate.py
│   │   ├── setting.py
│   │   ├── log.py
│   │   ├── truss_build.py
│   │   ├── user.py
│   ├── python/               # Shared Python code
│   │   ├── __init__.py
│   │   ├── health.py
│   │   ├── logging_middleware.py
│   │   ├── db.py
│   └── ts/                   # Generated TypeScript DTOs
│       ├── models.ts
├── deployment/
│   ├── docker-compose.yml    # Docker Compose configuration
│   ├── nginx.conf            # Nginx load balancer configuration
│   ├── deploy.sh             # Deployment script
│   ├── failover.sh           # HA failover script
│   ├── health-check.sh       # Health check script
│   ├── server.crt            # Self-signed cert
│   ├── server.key            # Self-signed key
├── docs/                     # Documentation
└── tests/                    # Tests
```

### 5.2 Progressive Development Steps

#### Step 1: Logging Microservice

**Purpose**: Implement the Logging Service first, as it’s used by all other microservices for error/action logging.

1. **Setup**:
   - Initialize:
     ```bash
     mkdir backend/logging
     cd backend/logging
     touch main.py requirements.txt
     ```
   - `requirements.txt`:
     ```text
     fastapi
     uvicorn[standard]
     sqlalchemy
     alembic
     psycopg2-binary
     shared
     pytest
     httpx
     ```
   - Install dependencies:
     ```bash
     pip install -r requirements.txt
     ```
2. **Shared Code**:
   - Create `shared/python/`:
     ```bash
     mkdir -p shared/python
     touch shared/python/__init__.py
     touch shared/python/health.py
     touch shared/python/logging_middleware.py
     touch shared/python/db.py
     ```
   - `shared/python/health.py`:
     ```python
     from fastapi import APIRouter, HTTPException
     router = APIRouter()
     @router.get("/health")
     async def health_check():
         return {"status": "healthy"}
     ```
   - `shared/python/logging_middleware.py`:
     ```python
     from fastapi import Request
     from starlette.middleware.base import BaseHTTPMiddleware
     import logging
     logging.basicConfig(level=logging.INFO)
     logger = logging.getLogger("tom")
     class LoggingMiddleware(BaseHTTPMiddleware):
         async def dispatch(self, request: Request, call_next):
             logger.info(f"Request: {request.method} {request.url}")
             response = await call_next(request)
             logger.info(f"Response: {response.status_code}")
             return response
     ```
   - `shared/python/db.py`:
     ```python
     from sqlalchemy import create_engine
     from sqlalchemy.orm import sessionmaker
     engine = create_engine('****************************************/tom')
     Session = sessionmaker(bind=engine)
     ```
   - `shared/setup.py`:
     ```python
     from setuptools import setup, find_packages
     setup(
         name="shared",
         version="1.0.0",
         packages=find_packages(),
         install_requires=["pydantic", "fastapi", "sqlalchemy", "psycopg2-binary"]
     )
     ```
   - Install shared package:
     ```bash
     pip install ./shared
     ```
3. **Shared DTOs**:
   - Create `shared/models/`:
     ```bash
     mkdir -p shared/models
     touch shared/models/__init__.py
     touch shared/models/system.py
     touch shared/models/log.py
     touch shared/models/user.py
     ```
   - `system.py`:
     ```python
     from pydantic import BaseModel
     class System(BaseModel):
         id: int
         name: str
     ```
   - `log.py`:
     ```python
     from pydantic import BaseModel
     from datetime import datetime
     from typing import Dict, Any
     class Log(BaseModel):
         id: int
         system_id: int
         user_id: int | None = None
         role: str | None = None
         action_type: str | None = None
         target_data: Dict[str, Any] | None = None
         timestamp: datetime
     ```
   - `user.py`:
     ```python
     from pydantic import BaseModel
     class User(BaseModel):
         id: int
         system_id: int
         username: str
         pin: str
         role: str
     ```
   - `__init__.py`:
     ```python
     from .system import System
     from .log import Log
     from .user import User
     ```
   - Generate TypeScript DTOs:
     ```bash
     pip install pydantic-to-typescript
     pydantic-to-typescript --pydantic-module shared.models --output shared/ts/models.ts
     ```
   - `shared/ts/models.ts` (partial):
     ```typescript
     export interface System {
       id: number;
       name: string;
     }
     export interface Log {
       id: number;
       system_id: number;
       user_id?: number;
       role?: string;
       action_type?: string;
       target_data?: { [key: string]: any };
       timestamp: string;
     }
     export interface User {
       id: number;
       system_id: number;
       username: string;
       pin: string;
       role: string;
     }
     ```
4. **Logging Service**:
   - `backend/logging/main.py`:
     ```python
     from fastapi import FastAPI
     from tom_shared.python.health import router as health_router
     from tom_shared.python.logging_middleware import LoggingMiddleware
     from tom_shared.python.db import Session
     from tom_shared.models.log import Log
     app = FastAPI()
     app.include_router(health_router)
     app.add_middleware(LoggingMiddleware)
     @app.post("/log")
     async def log_action(log: Log):
         with Session() as session:
             log_entry = Log(**log.dict())
             session.add(log_entry)
             session.commit()
         return {"status": "logged"}
     ```
5. **Database**:
   - Create `database/models/`:
     ```bash
     mkdir -p database/models
     touch database/models/__init__.py
     touch database/models/system.py
     touch database/models/log.py
     touch database/models/user.py
     ```
   - `system.py`:
     ```python
     from sqlalchemy import Column, Integer, String
     from sqlalchemy.ext.declarative import declarative_base
     Base = declarative_base()
     class System(Base):
         __tablename__ = 'systems'
         id = Column(Integer, primary_key=True)
         name = Column(String(100), nullable=False)
     ```
   - `log.py`:
     ```python
     from sqlalchemy import Column, Integer, ForeignKey, String, JSONB, DateTime
     from sqlalchemy.sql import func
     from sqlalchemy.ext.declarative import declarative_base
     Base = declarative_base()
     class Log(Base):
         __tablename__ = 'logs'
         id = Column(Integer, primary_key=True)
         system_id = Column(Integer, ForeignKey('systems.id'))
         user_id = Column(Integer)
         role = Column(String(50))
         action_type = Column(String(50))
         target_data = Column(JSONB)
         timestamp = Column(DateTime, server_default=func.now())
     ```
   - `user.py`:
     ```python
     from sqlalchemy import Column, Integer, ForeignKey, String
     from sqlalchemy.ext.declarative import declarative_base
     Base = declarative_base()
     class User(Base):
         __tablename__ = 'users'
         id = Column(Integer, primary_key=True)
         system_id = Column(Integer, ForeignKey('systems.id'))
         username = Column(String(100), nullable=False)
         pin = Column(String(4), nullable=False)
         role = Column(String(50), nullable=False)
     ```
   - `__init__.py`:
     ```python
     from .system import System
     from .log import Log
     from .user import User
     ```
   - Initialize Alembic:
     ```bash
     cd backend/logging
     alembic init migrations
     ```
   - Configure `alembic.ini`:
     ```
     [alembic]
     sqlalchemy.url = ****************************************/tom
     ```
   - Create migration (`backend/logging/migrations/versions/001_logging_schema.py`):
     ```python
     from alembic import op
     import sqlalchemy as sa
     def upgrade():
         op.create_table(
             'systems',
             sa.Column('id', sa.Integer, primary_key=True),
             sa.Column('name', sa.String(100), nullable=False)
         )
         op.create_table(
             'users',
             sa.Column('id', sa.Integer, primary_key=True),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('username', sa.String(100), nullable=False),
             sa.Column('pin', sa.String(4), nullable=False),
             sa.Column('role', sa.String(50), nullable=False)
         )
         op.create_table(
             'logs',
             sa.Column('id', sa.Integer, primary_key=True),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('user_id', sa.Integer),
             sa.Column('role', sa.String(50)),
             sa.Column('action_type', sa.String(50)),
             sa.Column('target_data', sa.JSONB),
             sa.Column('timestamp', sa.DateTime, server_default=sa.func.now())
         )
     def downgrade():
         op.drop_table('logs')
         op.drop_table('users')
         op.drop_table('systems')
     ```
   - Apply migration:
     ```bash
     docker-compose exec logging alembic upgrade head
     ```
6. **Tests**:
   - Create `backend/logging/tests/test_logging.py`:
     ```python
     from fastapi.testclient import TestClient
     from backend.logging.main import app
     client = TestClient(app)
     def test_health_endpoint():
         response = client.get("/health")
         assert response.status_code == 200
         assert response.json() == {"status": "healthy"}
     def test_log_action():
         log = {
             "id": 1,
             "system_id": 1,
             "user_id": 1,
             "role": "User",
             "action_type": "create_batch",
             "target_data": {"batch_id": 1},
             "timestamp": "2025-07-11T00:00:00"
         }
         response = client.post("/log", json=log)
         assert response.status_code == 200
         assert response.json() == {"status": "logged"}
     ```
   - Run tests:
     ```bash
     cd backend/logging
     pytest tests/test_logging.py
     ```
7. **Docker Compose** (partial):
   - Create `deployment/docker-compose.yml`:
     ```yaml
     version: "3.8"
     services:
       logging:
         build: ./backend/logging
         depends_on:
           - postgres
       postgres:
         image: postgres:15
         environment:
           POSTGRES_DB: tom
           POSTGRES_USER: user
           POSTGRES_PASSWORD: password
         volumes:
           - postgres_data:/var/lib/postgresql/data
         ports:
           - "5432:5432"
     volumes:
       postgres_data:
     ```
   - Start service:
     ```bash
     docker-compose up -d logging postgres
     ```

#### Step 2: Base UI with Login

**Purpose**: Implement the SolidJS main UI with PIN-based login, authentication, and RBAC, integrating with the Logging Service.

1. **Setup**:
   - Initialize:
     ```bash
     npx create-solid@latest frontend/main-ui
     cd frontend/main-ui
     npm install solid-socket jsonwebtoken
     ```
2. **Node.js API**:
   - Initialize:
     ```bash
     mkdir backend/api
     cd backend/api
     npm init -y
     npm install @nestjs/core @nestjs/common @nestjs/websockets @nestjs/platform-socket.io @nestjs/typeorm typeorm pg redis jsonwebtoken ../shared/ts
     ```
   - Configure TypeORM (`backend/api/src/ormconfig.ts`):
     ```typescript
     export const ormConfig = {
       type: "postgres",
       host: "postgres",
       database: "tom",
       username: "user",
       password: "password",
       entities: ["dist/**/*.entity{.ts,.js}"],
       migrations: ["dist/migrations/*.js"],
       migrationsRun: true,
     };
     ```
   - Create `AuthModule` (`backend/api/src/auth/auth.controller.ts`):
     ```typescript
     import { Controller, Post, Body, HttpException } from "@nestjs/common";
     import * as jwt from "jsonwebtoken";
     import { createClient } from "redis";
     @Controller("auth")
     export class AuthController {
       constructor(private readonly authService, private readonly redis) {}
       @Post("login")
       async login(@Body() body: { username: string; pin: string }) {
         const user = await this.authService.validateUser(
           body.username,
           body.pin
         );
         if (!user) throw new HttpException("Invalid credentials", 401);
         const token = jwt.sign(
           { user_id: user.id, role: user.role },
           "secret",
           { expiresIn: "1h" }
         );
         await this.redis.set(`session:${user.id}`, token, "EX", 3600);
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: user.system_id,
             user_id: user.id,
             role: user.role,
             action_type: "login",
             target_data: { username: body.username },
             timestamp: new Date().toISOString(),
           }),
         });
         return { token };
       }
     }
     ```
   - Create entities (`backend/api/src/entities/`):
     - `system.entity.ts`:
       ```typescript
       import { Entity, Column, PrimaryColumn } from "typeorm";
       @Entity("systems")
       export class System {
         @PrimaryColumn()
         id: number;
         @Column()
         name: string;
       }
       ```
     - `user.entity.ts`:
       ```typescript
       import {
         Entity,
         Column,
         PrimaryColumn,
         ManyToOne,
         JoinColumn,
       } from "typeorm";
       @Entity("users")
       export class User {
         @PrimaryColumn()
         id: number;
         @Column()
         system_id: number;
         @Column()
         username: string;
         @Column()
         pin: string;
         @Column()
         role: string;
         @ManyToOne(() => System)
         @JoinColumn({ name: "system_id" })
         system: System;
       }
       ```
     - `log.entity.ts`:
       ```typescript
       import {
         Entity,
         Column,
         PrimaryColumn,
         ManyToOne,
         JoinColumn,
       } from "typeorm";
       @Entity("logs")
       export class Log {
         @PrimaryColumn()
         id: number;
         @Column()
         system_id: number;
         @Column({ nullable: true })
         user_id: number;
         @Column({ nullable: true })
         role: string;
         @Column({ nullable: true })
         action_type: string;
         @Column({ type: "jsonb", nullable: true })
         target_data: any;
         @Column()
         timestamp: Date;
         @ManyToOne(() => System)
         @JoinColumn({ name: "system_id" })
         system: System;
       }
       ```
   - Create migration (`backend/api/src/migrations/001_auth_schema.ts`):
     ```typescript
     import { MigrationInterface, QueryRunner } from "typeorm";
     export class AuthSchema implements MigrationInterface {
       async up(queryRunner: QueryRunner): Promise<void> {
         await queryRunner.query(`
                 CREATE TABLE systems (
                     id INTEGER PRIMARY KEY,
                     name VARCHAR(100) NOT NULL
                 );
                 CREATE TABLE users (
                     id INTEGER PRIMARY KEY,
                     system_id INTEGER REFERENCES systems(id),
                     username VARCHAR(100) NOT NULL,
                     pin VARCHAR(4) NOT NULL,
                     role VARCHAR(50) NOT NULL
                 );
                 CREATE TABLE logs (
                     id INTEGER PRIMARY KEY,
                     system_id INTEGER REFERENCES systems(id),
                     user_id INTEGER,
                     role VARCHAR(50),
                     action_type VARCHAR(50),
                     target_data JSONB,
                     timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                 );
             `);
       }
       async down(queryRunner: QueryRunner): Promise<void> {
         await queryRunner.query(`
                 DROP TABLE logs;
                 DROP TABLE users;
                 DROP TABLE systems;
             `);
       }
     }
     ```
   - Apply migration:
     ```bash
     docker-compose exec api npx typeorm migration:run
     ```
3. **UI Components**:
   - Create `Login.jsx` (`frontend/main-ui/src/components/Login.jsx`):
     ```javascript
     import { createSignal } from "solid-js";
     export function Login() {
       const [username, setUsername] = createSignal("");
       const [pin, setPin] = createSignal("");
       async function handleLogin() {
         const response = await fetch("https://localhost:443/api/auth/login", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({ username: username(), pin: pin() }),
         });
         if (response.ok) {
           const { token } = await response.json();
           localStorage.setItem("token", token);
           // Redirect to main UI
         }
       }
       return (
         <div>
           <select onChange={(e) => setUsername(e.target.value)}>
             {/* Populate with usernames */}
           </select>
           <input
             type="password"
             value={pin()}
             onInput={(e) => setPin(e.target.value)}
           />
           <button onClick={handleLogin}>Login</button>
         </div>
       );
     }
     ```
4. **Tests**:
   - Create `frontend/main-ui/tests/login.test.jsx`:
     ```javascript
     import { render, fireEvent } from "@solidjs/testing-library";
     import { Login } from "../src/components/Login";
     test("should login successfully", async () => {
       const { getByRole } = render(() => <Login />);
       const button = getByRole("button", { name: /login/i });
       fireEvent.click(button);
       // Mock fetch and assert redirect
     });
     ```
   - Create `backend/api/src/auth/auth.controller.spec.ts`:
     ```typescript
     import { Test } from "@nestjs/testing";
     import { AuthController } from "./auth.controller";
     describe("AuthController", () => {
       let controller: AuthController;
       beforeEach(async () => {
         const module = await Test.createTestingModule({
           controllers: [AuthController],
           providers: [
             { provide: "AuthService", useValue: { validateUser: jest.fn() } },
             { provide: "redis", useValue: { set: jest.fn() } },
           ],
         }).compile();
         controller = module.get<AuthController>(AuthController);
       });
       it("should login successfully", async () => {
         const result = await controller.login({
           username: "test",
           pin: "1234",
         });
         expect(result).toHaveProperty("token");
       });
     });
     ```
   - Run tests:
     ```bash
     cd frontend/main-ui
     npm test
     cd backend/api
     npm test
     ```
5. **Docker Compose** (update):
   - Update `deployment/docker-compose.yml`:
     ```yaml
     version: "3.8"
     services:
       main-ui:
         build: ./frontend/main-ui
         ports: ["3000:3000"]
         depends_on:
           - nginx
       api:
         build: ./backend/api
         deploy:
           replicas: 3
           resources:
             limits:
               cpus: "1.0"
               memory: 2g
         depends_on:
           - postgres
           - redis
           - logging
       logging:
         build: ./backend/logging
         depends_on:
           - postgres
       postgres:
         image: postgres:15
         environment:
           POSTGRES_DB: tom
           POSTGRES_USER: user
           POSTGRES_PASSWORD: password
         volumes:
           - postgres_data:/var/lib/postgresql/data
         ports:
           - "5432:5432"
       redis:
         image: redis:7
         ports:
           - "6379:6379"
       nginx:
         image: nginx:latest
         network_mode: "host"
         volumes:
           - ./deployment/nginx.conf:/etc/nginx/nginx.conf
           - ./deployment/server.crt:/etc/nginx/server.crt
           - ./deployment/server.key:/etc/nginx/server.key
         depends_on:
           - api
     volumes:
       postgres_data:
     ```
   - Create `deployment/nginx.conf`:
     ```nginx
     upstream backend {
         ip_hash;
         server api:4000;
     }
     server {
         listen 443 ssl;
         ssl_certificate /etc/nginx/server.crt;
         ssl_certificate_key /etc/nginx/server.key;
         location / {
             proxy_pass http://backend;
             proxy_set_header Host $host;
             proxy_set_header X-Real-IP $remote_addr;
         }
         location /ws {
             proxy_pass http://backend;
             proxy_http_version 1.1;
             proxy_set_header Upgrade $http_upgrade;
             proxy_set_header Connection "upgrade";
         }
     }
     ```
   - Start services:
     ```bash
     docker-compose up -d main-ui api logging
     ```

#### Step 3: Settings Microservice

**Purpose**: Implement the Settings Service for managing system-specific settings, integrating with Logging Service.

1. **Setup**:
   - Initialize:
     ```bash
     mkdir backend/settings
     cd backend/settings
     touch main.py requirements.txt
     ```
   - `requirements.txt` (same as logging).
   - Install dependencies:
     ```bash
     pip install -r requirements.txt
     ```
2. **Shared DTOs**:
   - Add `setting.py` to `shared/models/`:
     ```python
     from pydantic import BaseModel
     from typing import Dict, Any
     class Setting(BaseModel):
         id: int
         system_id: int
         key: str
         value: Dict[str, Any]
         version: int = 1
     ```
   - Update `shared/models/__init__.py`:
     ```python
     from .system import System
     from .log import Log
     from .user import User
     from .setting import Setting
     ```
   - Update `shared/ts/models.ts`:
     ```typescript
     export interface Setting {
       id: number;
       system_id: number;
       key: string;
       value: { [key: string]: any };
       version: number;
     }
     ```
3. **Settings Service**:
   - `backend/settings/main.py`:
     ```python
     from fastapi import FastAPI
     from tom_shared.python.health import router as health_router
     from tom_shared.python.logging_middleware import LoggingMiddleware
     from tom_shared.python.db import Session
     from tom_shared.models.setting import Setting
     import redis
     import json
     app = FastAPI()
     app.include_router(health_router)
     app.add_middleware(LoggingMiddleware)
     r = redis.Redis(host='redis', port=6379)
     @app.post("/settings")
     async def update_setting(setting: Setting):
         with Session() as session:
             db_setting = Setting(**setting.dict())
             session.add(db_setting)
             session.commit()
         r.set(f"{setting.system_id}:{setting.key}", json.dumps(setting.value))
         r.publish(f"settings:{setting.system_id}", json.dumps(setting.dict()))
         await fetch('http://logging:80/log', {
             'method': 'POST',
             'headers': {'Content-Type': 'application/json'},
             'body': json.dumps({
                 'id': generate_id(),
                 'system_id': setting.system_id,
                 'action_type': 'update_setting',
                 'target_data': {'key': setting.key},
                 'timestamp': datetime.now().isoformat()
             })
         })
         return setting
     ```
4. **Database**:
   - Add `database/models/setting.py`:
     ```python
     from sqlalchemy import Column, Integer, ForeignKey, String, JSONB
     from sqlalchemy.ext.declarative import declarative_base
     Base = declarative_base()
     class Setting(Base):
         __tablename__ = 'settings'
         id = Column(Integer, primary_key=True)
         system_id = Column(Integer, ForeignKey('systems.id'))
         key = Column(String(100), nullable=False)
         value = Column(JSONB)
         version = Column(Integer, default=1)
     ```
   - Update `database/models/__init__.py`:
     ```python
     from .system import System
     from .log import Log
     from .user import User
     from .setting import Setting
     ```
   - Create migration (`backend/settings/migrations/versions/002_settings_schema.py`):
     ```python
     from alembic import op
     import sqlalchemy as sa
     def upgrade():
         op.create_table(
             'settings',
             sa.Column('id', sa.Integer, primary_key=True),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('key', sa.String(100), nullable=False),
             sa.Column('value', sa.JSONB),
             sa.Column('version', sa.Integer, default=1)
         )
     def downgrade():
         op.drop_table('settings')
     ```
   - Apply migration:
     ```bash
     docker-compose exec settings alembic upgrade head
     ```
5. **Tests**:
   - Create `backend/settings/tests/test_settings.py`:
     ```python
     from fastapi.testclient import TestClient
     from backend.settings.main import app
     client = TestClient(app)
     def test_update_setting():
         setting = {
             "id": 1,
             "system_id": 1,
             "key": "theme",
             "value": {"color": "blue"},
             "version": 1
         }
         response = client.post("/settings", json=setting)
         assert response.status_code == 200
         assert response.json() == setting
     def test_health_endpoint():
         response = client.get("/health")
         assert response.status_code == 200
         assert response.json() == {"status": "healthy"}
     ```
   - Run tests:
     ```bash
     cd backend/settings
     pytest tests/test_settings.py
     ```
6. **Docker Compose** (update):
   - Add `settings` to `deployment/docker-compose.yml`:
     ```yaml
     services:
       settings:
         build: ./backend/settings
         depends_on:
           - postgres
           - redis
           - logging
     ```

#### Step 3: File Parsing Microservice

**Purpose**: Implement the File Parsing Service for processing task files, integrating with Logging Service.

1. **Setup**:
   - Initialize:
     ```bash
     mkdir backend/file-parsing
     cd backend/file-parsing
     touch main.py requirements.txt
     ```
   - `requirements.txt` (same as logging).
2. **Shared DTOs**:
   - Add to `shared/models/`:
     - `tag.py`:
       ```python
       from pydantic import BaseModel
       class Tag(BaseModel):
           id: int
           name: str
       ```
     - `tags_relation.py`:
       ```python
       from pydantic import BaseModel
       from enum import Enum
       class EntityType(str, Enum):
           batch = "batch"
       class TagsRelation(BaseModel):
           id: int
           entity_type: EntityType
           type_id: int
           tag_id: int
       ```
     - `truss_file.py`:
       ```python
       from pydantic import BaseModel
       from datetime import datetime
       class TrussFile(BaseModel):
           id: int
           system_id: int
           file_data: bytes
           created_at: datetime
       ```
     - `batch.py`:
       ```python
       from pydantic import BaseModel
       class Batch(BaseModel):
           id: int
           system_id: int
           name: str
           description: str | None = None
           status: str
       ```
     - `truss.py`:
       ```python
       from pydantic import BaseModel
       from typing import Dict, Any
       class Truss(BaseModel):
           id: int
           batch_id: int
           system_id: int
           metadata: Dict[str, Any] | None = None
       ```
     - `board.py`:
       ```python
       from pydantic import BaseModel
       from typing import Dict, Any, List
       class Dimensions(BaseModel):
           thickness: float | None = None
           height: float | None = None
           length: float | None = None
           short_length: float | None = None
           long_length: float | None = None
           top_length: float | None = None
           center_length: float | None = None
           bottom_length: float | None = None
           nominal_length: float | None = None
           inside_length: float | None = None
       class Board(BaseModel):
           id: int
           truss_id: int
           system_id: int
           piece_type: str | None = None
           dimensions: Dimensions
           original_dimensions: Dimensions
           vertices: Dict[str, Any]
           original_vertices: Dict[str, Any]
           label: str | None = None
           member_grade: str | None = None
           member_species: str | None = None
           object_guid: str | None = None
           name: str | None = None
           attached_plates: List[int] | None = None
           intersecting_plates: List[int] | None = None
           splice_plate_id: int | None = None
           manual: bool = False
           direction: Dict[str, Any] | None = None
           place_location: Dict[str, Any] | None = None
           plate_edges: Dict[str, Any] | None = None
           pick_offset: Dict[str, Any] | None = None
           center_plate_ids: List[int] | None = None
           print_label: str | None = None
           angle_limits: Dict[str, Any] | None = None
           weight: float | None = None
           notes: str | None = None
           angle: float | None = None
           file_vertices: Dict[str, Any] | None = None
           file_rotation: Dict[str, Any] | None = None
           file_order: int | None = None
           modifications: List[Dict[str, Any]] = []
       ```
     - `plate.py`:
       ```python
       from pydantic import BaseModel
       from typing import Dict, Any, List
       class Plate(BaseModel):
           id: int
           truss_id: int
           system_id: int
           unique_id: str | None = None
           gauge: float | None = None
           description: str | None = None
           plate_height: float | None = None
           plate_width: float | None = None
           vertices: Dict[str, Any]
           original_vertices: Dict[str, Any]
           label: str | None = None
           offset: Dict[str, Any]
           original_offset: Dict[str, Any]
           center_point: Dict[str, Any]
           original_center_point: Dict[str, Any]
           object_guid: str | None = None
           dimensions: Dict[str, Any]
           original_dimensions: Dict[str, Any]
           rotation: Dict[str, Any]
           original_rotation: Dict[str, Any]
           file_center_point: Dict[str, Any] | None = None
           file_rotation: Dict[str, Any] | None = None
           file_vertices: Dict[str, Any] | None = None
           manual: bool = False
           modifications: List[Dict[str, Any]] = []
       ```
     - Update `shared/models/__init__.py`:
       ```python
       from .system import System
       from .log import Log
       from .user import User
       from .setting import Setting
       from .tag import Tag
       from .tags_relation import TagsRelation
       from .truss_file import TrussFile
       from .batch import Batch
       from .truss import Truss
       from .board import Board
       from .plate import Plate
       ```
     - Update `shared/ts/models.ts`:
       ```typescript
       export enum EntityType {
         batch = "batch",
       }
       export interface TagsRelation {
         id: number;
         entity_type: EntityType;
         type_id: number;
         tag_id: number;
       }
       export interface Dimensions {
         thickness?: number;
         height?: number;
         length?: number;
         short_length?: number;
         long_length?: number;
         top_length?: number;
         center_length?: number;
         bottom_length?: number;
         nominal_length?: number;
         inside_length?: number;
       }
       export interface Board {
         id: number;
         truss_id: number;
         system_id: number;
         piece_type?: string;
         dimensions: Dimensions;
         original_dimensions: Dimensions;
         vertices: { [key: string]: any };
         original_vertices: { [key: string]: any };
         label?: string;
         member_grade?: string;
         member_species?: string;
         object_guid?: string;
         name?: string;
         attached_plates?: number[];
         intersecting_plates?: number[];
         splice_plate_id?: number;
         manual: boolean;
         direction?: { [key: string]: any };
         place_location?: { [key: string]: any };
         plate_edges?: { [key: string]: any };
         pick_offset?: { [key: string]: any };
         center_plate_ids?: number[];
         print_label?: string;
         angle_limits?: { [key: string]: any };
         weight?: number;
         notes?: string;
         angle?: number;
         file_vertices?: { [key: string]: any };
         file_rotation?: { [key: string]: any };
         file_order?: number;
         modifications: { [key: string]: any }[];
       }
       export interface Plate {
         id: number;
         truss_id: number;
         system_id: number;
         unique_id?: string;
         gauge?: number;
         description?: string;
         plate_height?: number;
         plate_width?: number;
         vertices: { [key: string]: any };
         original_vertices: { [key: string]: any };
         label?: string;
         offset: { [key: string]: any };
         original_offset: { [key: string]: any };
         center_point: { [key: string]: any };
         original_center_point: { [key: string]: any };
         object_guid?: string;
         dimensions: { [key: string]: any };
         original_dimensions: { [key: string]: any };
         rotation: { [key: string]: any };
         original_rotation: { [key: string]: any };
         file_center_point?: { [key: string]: any };
         file_rotation?: { [key: string]: any };
         file_vertices?: { [key: string]: any };
         manual: boolean;
         modifications: { [key: string]: any }[];
       }
       export interface Tag {
         id: number;
         name: string;
       }
       export interface TrussFile {
         id: number;
         system_id: number;
         file_data: string; // Base64-encoded
         created_at: string;
       }
       export interface Batch {
         id: number;
         system_id: number;
         name: string;
         description?: string;
         status: string;
       }
       export interface Truss {
         id: number;
         batch_id: number;
         system_id: number;
         metadata?: { [key: string]: any };
       }
       ```
3. **File Parsing Service**:
   - `backend/file-parsing/main.py`:
     ```python
     from fastapi import FastAPI
     from tom_shared.python.health import router as health_router
     from tom_shared.python.logging_middleware import LoggingMiddleware
     from tom_shared.python.db import Session
     from tom_shared.models.truss_file import TrussFile
     from tom_shared.models.truss import Truss
     from tom_shared.models.board import Board
     from tom_shared.models.plate import Plate
     from tom_shared.models.tag import Tag
     from tom_shared.models.tags_relation import TagsRelation, EntityType
     import redis
     import json
     from datetime import datetime
     app = FastAPI()
     app.include_router(health_router)
     app.add_middleware(LoggingMiddleware)
     r = redis.Redis(host='redis', port=6379)
     @app.post("/parse")
     async def parse_file(truss_id: int, system_id: int):
         with Session() as session:
             truss_file = session.query(TrussFile).filter_by(id=truss_id).first()
             model = parse_truss_file(truss_file.file_data)
             truss = Truss(id=truss_id, batch_id=model["batch_id"], system_id=system_id, metadata=model["metadata"])
             session.add(truss)
             for board in model["boards"]:
                 board_entry = Board(
                     id=generate_id(),
                     truss_id=truss_id,
                     system_id=system_id,
                     piece_type=board.get("piece_type"),
                     dimensions=board["dimensions"],
                     original_dimensions=board["dimensions"],
                     vertices=board["vertices"],
                     original_vertices=board["vertices"],
                     label=board.get("label"),
                     member_grade=board.get("member_grade"),
                     member_species=board.get("member_species"),
                     object_guid=board.get("object_guid"),
                     name=board.get("name"),
                     attached_plates=board.get("attached_plates", []),
                     intersecting_plates=board.get("intersecting_plates", []),
                     splice_plate_id=board.get("splice_plate_id"),
                     manual=board.get("manual", False),
                     direction=board.get("direction", {}),
                     place_location=board.get("place_location", {}),
                     plate_edges=board.get("plate_edges", {}),
                     pick_offset=board.get("pick_offset", {}),
                     center_plate_ids=board.get("center_plate_ids", []),
                     print_label=board.get("print_label"),
                     angle_limits=board.get("angle_limits", {}),
                     weight=board.get("weight"),
                     notes=board.get("notes"),
                     angle=board.get("angle"),
                     file_vertices=board.get("file_vertices", {}),
                     file_rotation=board.get("file_rotation", {}),
                     file_order=board.get("file_order"),
                     modifications=[]
                 )
                 session.add(board_entry)
             for plate in model["plates"]:
                 plate_entry = Plate(
                     id=generate_id(),
                     truss_id=truss_id,
                     system_id=system_id,
                     unique_id=plate.get("unique_id"),
                     gauge=plate.get("gauge"),
                     description=plate.get("description"),
                     plate_height=plate.get("plate_height"),
                     plate_width=plate.get("plate_width"),
                     vertices=plate["vertices"],
                     original_vertices=plate["vertices"],
                     label=plate.get("label"),
                     offset=plate["offset"],
                     original_offset=plate["offset"],
                     center_point=plate["center_point"],
                     original_center_point=plate["center_point"],
                     object_guid=plate.get("object_guid"),
                     dimensions=plate["dimensions"],
                     original_dimensions=plate["dimensions"],
                     rotation=plate["rotation"],
                     original_rotation=plate["rotation"],
                     file_center_point=plate.get("file_center_point", {}),
                     file_rotation=plate.get("file_rotation", {}),
                     file_vertices=plate.get("file_vertices", {}),
                     manual=plate.get("manual", False),
                     modifications=[]
                 )
                 session.add(plate_entry)
             for tag_name in model["tags"]:
                 tag = session.query(Tag).filter_by(name=tag_name).first()
                 if not tag:
                     tag = Tag(id=generate_id(), name=tag_name)
                     session.add(tag)
                 relation = TagsRelation(
                     id=generate_id(),
                     entity_type=EntityType.batch,
                     type_id=model["batch_id"],
                     tag_id=tag.id
                 )
                 session.add(relation)
             session.commit()
             truss_data = {
                 "id": truss_id,
                 "boards": [b.dict() for b in model["boards"]],
                 "plates": [p.dict() for p in model["plates"]],
                 "metadata": model["metadata"]
             }
             r.set(f"truss:{system_id}:{truss_id}", json.dumps(truss_data))
             await fetch('http://logging:80/log', {
                 'method': 'POST',
                 'headers': {'Content-Type': 'application/json'},
                 'body': json.dumps({
                     'id': generate_id(),
                     'system_id': system_id,
                     'action_type': 'parse_file',
                     'target_data': {'truss_id': truss_id},
                     'timestamp': datetime.now().isoformat()
                 })
             })
         return {"model": model}
     ```
4. **Database**:
   - Add to `database/models/`:
     - `tag.py`:
       ```python
       from sqlalchemy import Column, Integer, String
       from sqlalchemy.ext.declarative import declarative_base
       Base = declarative_base()
       class Tag(Base):
           __tablename__ = 'tags'
           id = Column(Integer, primary_key=True)
           name = Column(String(100), nullable=False, unique=True)
       ```
     - `tags_relation.py`:
       ```python
       from sqlalchemy import Column, BigInteger, Integer, ForeignKey, Enum
       from sqlalchemy.ext.declarative import declarative_base
       import enum
       Base = declarative_base()
       class EntityType(enum.Enum):
           batch = "batch"
       class TagsRelation(Base):
           __tablename__ = 'tags_relations'
           id = Column(BigInteger, primary_key=True)
           entity_type = Column(Enum(EntityType), nullable=False)
           type_id = Column(BigInteger, nullable=False)
           tag_id = Column(Integer, ForeignKey('tags.id'))
       ```
     - `truss_file.py`:
       ```python
       from sqlalchemy import Column, BigInteger, Integer, ForeignKey, DateTime, LargeBinary
       from sqlalchemy.sql import func
       from sqlalchemy.ext.declarative import declarative_base
       Base = declarative_base()
       class TrussFile(Base):
           __tablename__ = 'truss_files'
           id = Column(BigInteger, primary_key=True)
           system_id = Column(Integer, ForeignKey('systems.id'))
           file_data = Column(LargeBinary, nullable=False)
           created_at = Column(DateTime, server_default=func.now())
       ```
     - `batch.py`:
       ```python
       from sqlalchemy import Column, BigInteger, Integer, String, ForeignKey
       from sqlalchemy.ext.declarative import declarative_base
       Base = declarative_base()
       class Batch(Base):
           __tablename__ = 'batches'
           id = Column(BigInteger, primary_key=True)
           system_id = Column(Integer, ForeignKey('systems.id'))
           name = Column(String(100), nullable=False)
           description = Column(String)
           status = Column(String(20), nullable=False)
       ```
     - `truss.py`:
       ```python
       from sqlalchemy import Column, BigInteger, Integer, ForeignKey, JSONB
       from sqlalchemy.ext.declarative import declarative_base
       Base = declarative_base()
       class Truss(Base):
           __tablename__ = 'trusses'
           id = Column(BigInteger, primary_key=True)
           batch_id = Column(BigInteger, ForeignKey('batches.id'))
           system_id = Column(Integer, ForeignKey('systems.id'))
           metadata = Column(JSONB)
       ```
     - `board.py`:
       ```python
       from sqlalchemy import Column, BigInteger, Integer, ForeignKey, JSONB, String, Boolean, Float
       from sqlalchemy.ext.declarative import declarative_base
       Base = declarative_base()
       class Board(Base):
           __tablename__ = 'boards'
           id = Column(BigInteger, primary_key=True)
           truss_id = Column(BigInteger, ForeignKey('trusses.id'))
           system_id = Column(Integer, ForeignKey('systems.id'))
           piece_type = Column(String(50))
           dimensions = Column(JSONB)
           original_dimensions = Column(JSONB)
           vertices = Column(JSONB)
           original_vertices = Column(JSONB)
           label = Column(String(100))
           member_grade = Column(String(50))
           member_species = Column(String(50))
           object_guid = Column(String(36))
           name = Column(String(100))
           attached_plates = Column(JSONB)
           intersecting_plates = Column(JSONB)
           splice_plate_id = Column(BigInteger)
           manual = Column(Boolean)
           direction = Column(JSONB)
           place_location = Column(JSONB)
           plate_edges = Column(JSONB)
           pick_offset = Column(JSONB)
           center_plate_ids = Column(JSONB)
           print_label = Column(String(100))
           angle_limits = Column(JSONB)
           weight = Column(Float)
           notes = Column(String)
           angle = Column(Float)
           file_vertices = Column(JSONB)
           file_rotation = Column(JSONB)
           file_order = Column(Integer)
           modifications = Column(JSONB, default=lambda: [])
       ```
     - `plate.py`:
       ```python
       from sqlalchemy import Column, BigInteger, Integer, ForeignKey, JSONB, String, Boolean, Float
       from sqlalchemy.ext.declarative import declarative_base
       Base = declarative_base()
       class Plate(Base):
           __tablename__ = 'plates'
           id = Column(BigInteger, primary_key=True)
           truss_id = Column(BigInteger, ForeignKey('trusses.id'))
           system_id = Column(Integer, ForeignKey('systems.id'))
           unique_id = Column(String(36))
           gauge = Column(Float)
           description = Column(String)
           plate_height = Column(Float)
           plate_width = Column(Float)
           vertices = Column(JSONB)
           original_vertices = Column(JSONB)
           label = Column(String(100))
           offset = Column(JSONB)
           original_offset = Column(JSONB)
           center_point = Column(JSONB)
           original_center_point = Column(JSONB)
           object_guid = Column(String(36))
           dimensions = Column(JSONB)
           original_dimensions = Column(JSONB)
           rotation = Column(JSONB)
           original_rotation = Column(JSONB)
           file_center_point = Column(JSONB)
           file_rotation = Column(JSONB)
           file_vertices = Column(JSONB)
           manual = Column(Boolean)
           modifications = Column(JSONB, default=lambda: [])
       ```
     - Update `database/models/__init__.py`:
       ```python
       from .system import System
       from .log import Log
       from .user import User
       from .setting import Setting
       from .tag import Tag
       from .tags_relation import TagsRelation
       from .truss_file import TrussFile
       from .batch import Batch
       from .truss import Truss
       from .board import Board
       from .plate import Plate
       ```
   - Create migration (`backend/file-parsing/migrations/versions/003_file_parsing_schema.py`):
     ```python
     from alembic import op
     import sqlalchemy as sa
     def upgrade():
         op.execute("CREATE TYPE entity_type AS ENUM ('batch')")
         op.create_table(
             'tags',
             sa.Column('id', sa.Integer, primary_key=True),
             sa.Column('name', sa.String(100), nullable=False, unique=True)
         )
         op.create_table(
             'truss_files',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('file_data', sa.LargeBinary, nullable=False),
             sa.Column('created_at', sa.DateTime, server_default=sa.func.now())
         )
         op.create_table(
             'batches',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('name', sa.String(100), nullable=False),
             sa.Column('description', sa.String),
             sa.Column('status', sa.String(20), nullable=False)
         )
         op.create_table(
             'tags_relations',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('entity_type', sa.Enum('batch', name='entity_type'), nullable=False),
             sa.Column('type_id', sa.BigInteger, nullable=False),
             sa.Column('tag_id', sa.Integer, sa.ForeignKey('tags.id'))
         )
         op.create_table(
             'trusses',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('batch_id', sa.BigInteger, sa.ForeignKey('batches.id')),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('metadata', sa.JSONB)
         )
         op.create_table(
             'boards',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('truss_id', sa.BigInteger, sa.ForeignKey('trusses.id')),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('piece_type', sa.String(50)),
             sa.Column('dimensions', sa.JSONB),
             sa.Column('original_dimensions', sa.JSONB),
             sa.Column('vertices', sa.JSONB),
             sa.Column('original_vertices', sa.JSONB),
             sa.Column('label', sa.String(100)),
             sa.Column('member_grade', sa.String(50)),
             sa.Column('member_species', sa.String(50)),
             sa.Column('object_guid', sa.String(36)),
             sa.Column('name', sa.String(100)),
             sa.Column('attached_plates', sa.JSONB),
             sa.Column('intersecting_plates', sa.JSONB),
             sa.Column('splice_plate_id', sa.BigInteger),
             sa.Column('manual', sa.Boolean),
             sa.Column('direction', sa.JSONB),
             sa.Column('place_location', sa.JSONB),
             sa.Column('plate_edges', sa.JSONB),
             sa.Column('pick_offset', sa.JSONB),
             sa.Column('center_plate_ids', sa.JSONB),
             sa.Column('print_label', sa.String(100)),
             sa.Column('angle_limits', sa.JSONB),
             sa.Column('weight', sa.Float),
             sa.Column('notes', sa.String),
             sa.Column('angle', sa.Float),
             sa.Column('file_vertices', sa.JSONB),
             sa.Column('file_rotation', sa.JSONB),
             sa.Column('file_order', sa.Integer),
             sa.Column('modifications', sa.JSONB, default=lambda: [])
         )
         op.create_table(
             'plates',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('truss_id', sa.BigInteger, sa.ForeignKey('trusses.id')),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('unique_id', sa.String(36)),
             sa.Column('gauge', sa.Float),
             sa.Column('description', sa.String),
             sa.Column('plate_height', sa.Float),
             sa.Column('plate_width', sa.Float),
             sa.Column('vertices', sa.JSONB),
             sa.Column('original_vertices', sa.JSONB),
             sa.Column('label', sa.String(100)),
             sa.Column('offset', sa.JSONB),
             sa.Column('original_offset', sa.JSONB),
             sa.Column('center_point', sa.JSONB),
             sa.Column('original_center_point', sa.JSONB),
             sa.Column('object_guid', sa.String(36)),
             sa.Column('dimensions', sa.JSONB),
             sa.Column('original_dimensions', sa.JSONB),
             sa.Column('rotation', sa.JSONB),
             sa.Column('original_rotation', sa.JSONB),
             sa.Column('file_center_point', sa.JSONB),
             sa.Column('file_rotation', sa.JSONB),
             sa.Column('file_vertices', sa.JSONB),
             sa.Column('manual', sa.Boolean),
             sa.Column('modifications', sa.JSONB, default=lambda: [])
         )
         op.create_index('idx_truss_files_system_id', 'truss_files', ['system_id', 'id'])
         op.create_index('idx_batches_system_id', 'batches', ['system_id', 'id'])
         op.create_index('idx_tags_name', 'tags', ['name'])
         op.create_index('idx_tags_relations_entity_type', 'tags_relations', ['entity_type', 'type_id'])
         op.create_index('idx_tags_relations_tag_id', 'tags_relations', ['tag_id'])
         op.create_index('idx_boards_truss_id', 'boards', ['truss_id', 'system_id'])
         op.create_index('idx_plates_truss_id', 'plates', ['truss_id', 'system_id'])
         op.execute("""
             CREATE TABLE boards_p1 PARTITION OF boards FOR VALUES FROM (0) TO (1000000);
             CREATE TABLE plates_p1 PARTITION OF plates FOR VALUES FROM (0) TO (1000000);
         """)
     def downgrade():
         op.execute("DROP TABLE boards CASCADE")
         op.execute("DROP TABLE plates CASCADE")
         op.drop_table('trusses')
         op.drop_table('tags_relations')
         op.drop_table('batches')
         op.drop_table('truss_files')
         op.drop_table('tags')
         op.execute("DROP TYPE entity_type")
     ```
   - Apply migration:
     ```bash
     docker-compose exec file-parsing alembic upgrade head
     ```
5. **Tests**:
   - Create `backend/file-parsing/tests/test_file_parsing.py`:
     ```python
     from fastapi.testclient import TestClient
     from backend.file_parsing.main import app
     client = TestClient(app)
     def test_parse_file():
         response = client.post("/parse", json={"truss_id": 1, "system_id": 1})
         assert response.status_code == 200
         assert "model" in response.json()
     def test_health_endpoint():
         response = client.get("/health")
         assert response.status_code == 200
         assert response.json() == {"status": "healthy"}
     ```
   - Run tests:
     ```bash
     cd backend/file-parsing
     pytest tests/test_file_parsing.py
     ```
6. **Docker Compose** (update):
   - Add `file-parsing` to `deployment/docker-compose.yml`:
     ```yaml
     services:
       file-parsing:
         build: ./backend/file-parsing
         depends_on:
           - postgres
           - redis
           - logging
     ```

#### Step 4: Rules Engine Microservice

**Purpose**: Implement the Rules Engine Service for applying JSON-based rules, integrating with Logging Service.

1. **Setup**:
   - Initialize:
     ```bash
     mkdir backend/rules-engine
     cd backend/rules-engine
     touch main.py requirements.txt
     ```
   - `requirements.txt` (same as logging).
2. **Rules Engine Service**:
   - `backend/rules-engine/main.py`:
     ```python
     from fastapi import FastAPI
     from tom_shared.python.health import router as health_router
     from tom_shared.python.logging_middleware import LoggingMiddleware
     from tom_shared.python.db import Session
     from tom_shared.models.truss import Truss
     from tom_shared.models.board import Board
     from tom_shared.models.plate import Plate
     from json_rules_engine import RuleEngine
     from datetime import datetime
     import redis
     import json
     app = FastAPI()
     app.include_router(health_router)
     app.add_middleware(LoggingMiddleware)
     engine = RuleEngine()
     r = redis.Redis(host='redis', port=6379)
     @app.post("/apply-rules")
     async def apply_rules(truss: Truss):
         rules = await get_rules()
         engine.add_rules(rules)
         result = engine.run(truss.dict())
         with Session() as session:
             for board in result.get("boards", []):
                 board_entry = session.query(Board).filter_by(id=board["id"]).first()
                 modification = {
                     "timestamp": datetime.now().isoformat(),
                     "user_id": null,
                     "dimensions": board["dimensions"],
                     "vertices": board["vertices"],
                     "rotation": board["rotation"]
                 }
                 board_entry.modifications.append(modification)
                 board_entry.dimensions = board["dimensions"]
                 board_entry.vertices = board["vertices"]
                 board_entry.rotation = board["rotation"]
                 session.add(board_entry)
             for plate in result.get("plates", []):
                 plate_entry = session.query(Plate).filter_by(id=plate["id"]).first()
                 modification = {
                     "timestamp": datetime.now().isoformat(),
                     "user_id": null,
                     "dimensions": plate["dimensions"],
                     "vertices": plate["vertices"],
                     "rotation": plate["rotation"],
                     "offset": plate["offset"],
                     "center_point": plate["center_point"]
                 }
                 plate_entry.modifications.append(modification)
                 plate_entry.dimensions = plate["dimensions"]
                 plate_entry.vertices = plate["vertices"]
                 plate_entry.rotation = plate["rotation"]
                 plate_entry.offset = plate["offset"]
                 plate_entry.center_point = plate["center_point"]
                 session.add(plate_entry)
             session.commit()
             r.del(f"truss:{truss.system_id}:{truss.id}")
             await fetch('http://logging:80/log', {
                 'method': 'POST',
                 'headers': {'Content-Type': 'application/json'},
                 'body': json.dumps({
                     'id': generate_id(),
                     'system_id': truss.system_id,
                     'action_type': 'apply_rules',
                     'target_data': {'truss_id': truss.id},
                     'timestamp': datetime.now().isoformat()
                 })
             })
         return {"manual": result}
     ```
3. **Tests**:
   - Create `backend/rules-engine/tests/test_rules_engine.py`:
     ```python
     from fastapi.testclient import TestClient
     from backend.rules_engine.main import app
     client = TestClient(app)
     def test_apply_rules():
         truss = {
             "id": 1,
             "batch_id": 1,
             "system_id": 1,
             "metadata": {}
         }
         response = client.post("/apply-rules", json=truss)
         assert response.status_code == 200
         assert "manual" in response.json()
     def test_health_endpoint():
         response = client.get("/health")
         assert response.status_code == 200
         assert response.json() == {"status": "healthy"}
     ```
   - Run tests:
     ```bash
     cd backend/rules-engine
     pytest tests/test_rules_engine.py
     ```
4. **Docker Compose** (update):
   - Add `rules-engine` to `deployment/docker-compose.yml`:
     ```yaml
     services:
       rules-engine:
         build: ./backend/rules-engine
         depends_on:
           - postgres
           - redis
           - logging
     ```

#### Step 5: Robotic Integration Microservice

**Purpose**: Implement the Robotic Integration Service for WebSocket communication and build snapshots, integrating with Logging Service.

1. **Setup**:
   - Initialize:
     ```bash
     mkdir backend/robotic-integration
     cd backend/robotic-integration
     touch main.py requirements.txt
     ```
   - `requirements.txt` (same as logging, add `python-socketio`).
2. **Shared DTOs**:
   - Add `truss_build.py` to `shared/models/`:
     ```python
     from pydantic import BaseModel
     from datetime import datetime
     from typing import Dict, Any, List
     class TrussBuild(BaseModel):
         id: int
         truss_id: int
         system_id: int
         build_timestamp: datetime
         boards_snapshot: List[Dict[str, Any]]
         plates_snapshot: List[Dict[str, Any]]
         robot_data: Dict[str, Any]
     ```
   - Update `shared/models/__init__.py`:
     ```python
     from .system import System
     from .log import Log
     from .user import User
     from .setting import Setting
     from .tag import Tag
     from .tags_relation import TagsRelation
     from .truss_file import TrussFile
     from .batch import Batch
     from .truss import Truss
     from .board import Board
     from .plate import Plate
     from .truss_build import TrussBuild
     ```
   - Update `shared/ts/models.ts`:
     ```typescript
     export interface TrussBuild {
       id: number;
       truss_id: number;
       system_id: number;
       build_timestamp: string;
       boards_snapshot: { [key: string]: any }[];
       plates_snapshot: { [key: string]: any }[];
       robot_data: { [key: string]: any };
     }
     ```
3. **Robotic Integration Service**:
   - `backend/robotic-integration/main.py`:
     ```python
     from fastapi import FastAPI
     import socketio
     from tom_shared.python.health import router as health_router
     from tom_shared.python.logging_middleware import LoggingMiddleware
     from tom_shared.python.db import Session
     from tom_shared.models.truss import Truss
     from tom_shared.models.board import Board
     from tom_shared.models.plate import Plate
     from tom_shared.models.truss_build import TrussBuild
     import redis
     import json
     from datetime import datetime
     app = FastAPI()
     app.include_router(health_router)
     app.add_middleware(LoggingMiddleware)
     sio = socketio.AsyncServer()
     sio.attach(app)
     r = redis.Redis(host='redis', port=6379)
     @sio.on("truss_request")
     async def handle_request(sid, data):
         cached = r.get(f"truss:{data['system_id']}:{data['truss_id']}")
         if cached:
             await sio.emit("truss_data", json.loads(cached), room=sid)
             return
         with Session() as session:
             truss = session.query(Truss).filter_by(id=data["truss_id"]).first()
             boards = session.query(Board).filter_by(truss_id=data["truss_id"]).all()
             plates = session.query(Plate).filter_by(truss_id=data["truss_id"]).all()
             truss_data = {"id": truss.id, "boards": [b.__dict__ for b in boards], "plates": [p.__dict__ for p in plates]}
             r.set(f"truss:{data['system_id']}:{data['truss_id']}", json.dumps(truss_data))
             await sio.emit("truss_data", truss_data, room=sid)
             await fetch('http://logging:80/log', {
                 'method': 'POST',
                 'headers': {'Content-Type': 'application/json'},
                 'body': json.dumps({
                     'id': generate_id(),
                     'system_id': data['system_id'],
                     'action_type': 'truss_request',
                     'target_data': {'truss_id': data['truss_id']},
                     'timestamp': datetime.now().isoformat()
                 })
             })
     @sio.on("status_update")
     async def handle_update(sid, data):
         with Session() as session:
             if data["status"] == "Completed":
                 boards = session.query(Board).filter_by(truss_id=data["truss_id"]).all()
                 plates = session.query(Plate).filter_by(truss_id=data["truss_id"]).all()
                 build = TrussBuild(
                     id=generate_id(),
                     truss_id=data["truss_id"],
                     system_id=data["system_id"],
                     build_timestamp=datetime.now(),
                     boards_snapshot=[b.__dict__ for b in boards],
                     plates_snapshot=[p.__dict__ for p in plates],
                     robot_data=data["robot_data"]
                 )
                 session.add(build)
             session.commit()
             await fetch('http://logging:80/log', {
                 'method': 'POST',
                 'headers': {'Content-Type': 'application/json'},
                 'body': json.dumps({
                     'id': generate_id(),
                     'system_id': data['system_id'],
                     'action_type': 'status_update',
                     'target_data': {'truss_id': data["truss_id"], 'status': data["status"]},
                     'timestamp': datetime.now().isoformat()
                 })
             })
         await sio.emit(f"instance_{data['system_id']}", data)
     ```
4. **Database**:
   - Add `database/models/truss_build.py`:
     ```python
     from sqlalchemy import Column, BigInteger, Integer, ForeignKey, JSONB, DateTime
     from sqlalchemy.sql import func
     from sqlalchemy.ext.declarative import declarative_base
     Base = declarative_base()
     class TrussBuild(Base):
         __tablename__ = 'truss_builds'
         id = Column(BigInteger, primary_key=True)
         truss_id = Column(BigInteger, ForeignKey('trusses.id'))
         system_id = Column(Integer, ForeignKey('systems.id'))
         build_timestamp = Column(DateTime, server_default=func.now())
         boards_snapshot = Column(JSONB)
         plates_snapshot = Column(JSONB)
         robot_data = Column(JSONB)
     ```
   - Update `database/models/__init__.py`:
     ```python
     from .system import System
     from .log import Log
     from .user import User
     from .setting import Setting
     from .tag import Tag
     from .tags_relation import TagsRelation
     from .truss_file import TrussFile
     from .batch import Batch
     from .truss import Truss
     from .board import Board
     from .plate import Plate
     from .truss_build import TrussBuild
     ```
   - Create migration (`backend/robotic-integration/migrations/versions/004_robotic_integration_schema.py`):
     ```python
     from alembic import op
     import sqlalchemy as sa
     def upgrade():
         op.create_table(
             'truss_builds',
             sa.Column('id', sa.BigInteger, primary_key=True),
             sa.Column('truss_id', sa.BigInteger, sa.ForeignKey('trusses.id')),
             sa.Column('system_id', sa.Integer, sa.ForeignKey('systems.id')),
             sa.Column('build_timestamp', sa.DateTime, server_default=sa.func.now()),
             sa.Column('boards_snapshot', sa.JSONB),
             sa.Column('plates_snapshot', sa.JSONB),
             sa.Column('robot_data', sa.JSONB)
         )
         op.create_index('idx_truss_builds_truss_id', 'truss_builds', ['truss_id', 'system_id'])
     def downgrade():
         op.drop_table('truss_builds')
     ```
   - Apply migration:
     ```bash
     docker-compose exec robotic-integration alembic upgrade head
     ```
5. **Tests**:
   - Create `backend/robotic-integration/tests/test_robotic_integration.py`:
     ```python
     from fastapi.testclient import TestClient
     from backend.robotic_integration.main import app
     client = TestClient(app)
     def test_health_endpoint():
         response = client.get("/health")
         assert response.status_code == 200
         assert response.json() == {"status": "healthy"}
     ```
   - Run tests:
     ```bash
     cd backend/robotic-integration
     pytest tests/test_robotic_integration.py
     ```
6. **Docker Compose** (update):
   - Add `robotic-integration` to `deployment/docker-compose.yml`:
     ```yaml
     services:
       robotic-integration:
         build: ./backend/robotic-integration
         depends_on:
           - postgres
           - redis
           - logging
     ```

#### Step 6: Complete Node.js API and UI

**Purpose**: Complete the Node.js API for batch/truss management and history, and finalize the main UI and board display.

1. **Node.js API**:
   - Update `backend/api/src/app.module.ts`:
     ```typescript
     import { Module } from "@nestjs/common";
     import { TypeOrmModule } from "@nestjs/typeorm";
     import { AuthController } from "./auth/auth.controller";
     import { BatchesController } from "./batches/batches.controller";
     import { TrussesController } from "./trusses/trusses.controller";
     import { HistoryController } from "./history/history.controller";
     import { TagsRelationsController } from "./tags-relations/tags-relations.controller";
     @Module({
       imports: [
         TypeOrmModule.forRoot({
           type: "postgres",
           host: "postgres",
           database: "tom",
           username: "user",
           password: "password",
           entities: ["dist/**/*.entity{.ts,.js}"],
           migrations: ["dist/migrations/*.js"],
           migrationsRun: true,
         }),
       ],
       controllers: [
         AuthController,
         BatchesController,
         TrussesController,
         HistoryController,
         TagsRelationsController,
       ],
     })
     export class AppModule {}
     ```
   - Create `batches.controller.ts`:
     ```typescript
     import { Controller, Get, Param, Query, Post, Body } from "@nestjs/common";
     import { Batch, TagsRelation } from "shared/ts";
     @Controller("batches")
     export class BatchesController {
       constructor(
         private readonly batchService,
         private readonly tagsRelationService,
         private readonly redis
       ) {}
       @Get(":id")
       async getBatch(
         @Param("id") batchId: number,
         @Query("system_id") systemId: number
       ) {
         const cached = await this.redis.get(`batch:{systemId}:{batchId}`);
         if (cached) return JSON.parse(cached);
         const batch = await this.batchService.findOne(batchId, systemId);
         const tags = await this.tagsRelationService.findTagsByEntity(
           "batch",
           batchId,
           systemId
         );
         const batchData = { ...batch, tags };
         await this.redis.set(
           `batch:{systemId}:{batchId}`,
           JSON.stringify(batchData)
         );
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: systemId,
             action_type: "get_batch",
             target_data: { batch_id: batchId },
             timestamp: new Date().toISOString(),
           }),
         });
         return batchData;
       }
       @Post()
       async createBatch(@Body() batch: Batch) {
         await this.batchService.create(batch);
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: batch.system_id,
             action_type: "create_batch",
             target_data: { batch_id: batch.id },
             timestamp: new Date().toISOString(),
           }),
         });
         return batch;
       }
     }
     @Controller("tags_relations")
     export class TagsRelationsController {
       constructor(
         private readonly tagsRelationService,
         private readonly redis
       ) {}
       @Post()
       async create(@Body() relation: TagsRelation) {
         await this.tagsRelationService.create(relation);
         await this.redis.del(`batch:{relation.system_id}:{relation.type_id}`);
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: relation.system_id,
             action_type: "create_tag_relation",
             target_data: { relation_id: relation.id },
             timestamp: new Date().toISOString(),
           }),
         });
         return relation;
       }
     }
     ```
   - Create `trusses.controller.ts`:
     ```typescript
     import { Controller, Get, Param, Query, Post, Body } from "@nestjs/common";
     import { Truss, Board, Plate } from "shared/ts";
     @Controller("trusses")
     export class TrussesController {
       constructor(
         private readonly trussService,
         private readonly boardService,
         private readonly plateService,
         private readonly redis
       ) {}
       @Get(":id")
       async getTruss(
         @Param("id") trussId: number,
         @Query("system_id") systemId: number
       ) {
         const cached = await this.redis.get(`truss:{systemId}:{trussId}`);
         if (cached) return JSON.parse(cached);
         const truss = await this.trussService.findOne(trussId, systemId);
         const boards: Board[] = await this.boardService.findByTrussId(
           trussId,
           systemId
         );
         const plates: Plate[] = await this.plateService.findByTrussId(
           trussId,
           systemId
         );
         const trussData = { ...truss, boards, plates };
         await this.redis.set(
           `truss:{systemId}:{trussId}`,
           JSON.stringify(trussData)
         );
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: systemId,
             action_type: "get_truss",
             target_data: { truss_id: trussId },
             timestamp: new Date().toISOString(),
           }),
         });
         return trussData;
       }
       @Post("files")
       async uploadFile(@Body() body: { system_id: number; file: string }) {
         const trussId = generateId();
         const buffer = Buffer.from(body.file, "base64");
         await fetch("http://file-parsing:80/parse", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             truss_id: trussId,
             system_id: body.system_id,
           }),
         });
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: body.system_id,
             action_type: "upload_file",
             target_data: { truss_id: trussId },
             timestamp: new Date().toISOString(),
           }),
         });
         return { truss_id: trussId };
       }
     }
     ```
   - Create `history.controller.ts`:
     ```typescript
     import { Controller, Get, Param, Query } from "@nestjs/common";
     import { Truss, Board, Plate, TrussBuild } from "shared/ts";
     @Controller("trusses")
     export class HistoryController {
       constructor(
         private readonly trussService,
         private readonly boardService,
         private readonly plateService,
         private readonly buildService,
         private readonly redis
       ) {}
       @Get(":id/history")
       async getTrussHistory(
         @Param("id") trussId: number,
         @Query("system_id") systemId: number
       ) {
         const cached = await this.redis.get(`history:{systemId}:{trussId}`);
         if (cached) return JSON.parse(cached);
         const truss = await this.trussService.findOne(trussId, systemId);
         const boards = await this.boardService.findByTrussId(
           trussId,
           systemId
         );
         const plates = await this.plateService.findByTrussId(
           trussId,
           systemId
         );
         const builds = await this.buildService.findByTrussId(
           trussId,
           systemId
         );
         const history = { truss, boards, plates, builds };
         await this.redis.set(
           `history:{systemId}:{trussId}`,
           JSON.stringify(history)
         );
         await fetch("http://logging:80/log", {
           method: "POST",
           headers: { "Content-Type": "application/json" },
           body: JSON.stringify({
             id: generateId(),
             system_id: systemId,
             action_type: "get_history",
             target_data: { truss_id: trussId },
             timestamp: new Date().toISOString(),
           }),
         });
         return history;
       }
     }
     ```
2. **UI Components**:
   - Create `BatchCreate.jsx`, `BatchEdit.jsx`, `RulesConfig.jsx`, `Settings.jsx`, `TrussHistory.jsx` (as previously defined).
3. **Tests**:
   - Create tests for API endpoints and UI components (similar to Step 2).
   - Run tests:
     ```bash
     cd frontend/main-ui
     npm test
     cd backend/api
     npm test
     ```
4. **Docker Compose** (final):
   - Update `deployment/docker-compose.yml`:
     ```yaml
     version: "3.8"
     services:
       main-ui:
         build: ./frontend/main-ui
         ports: ["3000:3000"]
         depends_on:
           - nginx
       board-display:
         build: ./frontend/board-display
         ports: ["3001:3001"]
         depends_on:
           - nginx
       api:
         build: ./backend/api
         deploy:
           replicas: 3
           resources:
             limits:
               cpus: "1.0"
               memory: 1g
         healthcheck:
           test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
           interval: 30s
           timeout: 10s
           retries: 3
         depends_on:
           - postgres
           - redis
           - logging
           - settings
           - file-parsing
           - rules-engine
           - robotic-integration
       file-parsing:
         build: ./backend/file-parsing
         depends_on:
           - postgres
           - redis
           - logging
       rules-engine:
         build: ./backend/rules-engine
         depends_on:
           - postgres
           - redis
           - logging
       settings:
         build: ./backend/settings
         depends_on:
           - postgres
           - redis
           - logging
       logging:
         build: ./backend/logging
         depends_on:
           - postgres
       robotic-integration:
         build: ./backend/robotic-integration
         depends_on:
           - postgres
           - redis
           - logging
       postgres:
         image: postgres:15
         environment:
           POSTGRES_DB: tom
           POSTGRES_USER: user
           POSTGRES_PASSWORD: password
         volumes:
           - postgres_data:/var/lib/postgresql/data
         ports:
           - "5432:5432"
       redis:
         image: redis:7
         ports:
           - "6379:6379"
       nginx:
         image: nginx:latest
         network_mode: "host"
         volumes:
           - ./deployment/nginx.conf:/etc/nginx/nginx.conf
           - ./deployment/server.crt:/etc/nginx/server.crt
           - ./deployment/server.key:/etc/nginx/server.key
         depends_on:
           - api
     volumes:
       postgres_data:
     ```

#### Step 7: High Availability

- **Hot Standby**:
  - Deploy a second `docker-compose.yml` on standby PC.
  - Use Patroni for PostgreSQL replication.
  - Create `failover.sh`:
    ```bash
    #!/bin/bash
    docker-compose exec patroni patronictl promote
    redis-cli SLAVEOF NO ONE
    ip addr add *************/24 dev eth0
    docker-compose up -d
    ```
  - Create `health-check.sh`:
    ```bash
    #!/bin/bash
    if ! curl -f http://*************:80/health; then
        echo "Primary down!"
        ./failover.sh
    fi
    ```
- **Non-HA**:
  - Configure backups:
    ```bash
    docker-compose exec postgres pg_dump -U user tom > backup.sql
    ```

#### Step 8: Deployment

- **Docker Compose**:
  - Create `deploy.sh`:
    ```bash
    #!/bin/bash
    docker-compose pull
    docker-compose up -d --no-deps --scale api=3
    docker-compose rm -f old_api
    docker-compose exec api npx typeorm migration:run
    docker-compose exec logging alembic upgrade head
    docker-compose exec settings alembic upgrade head
    docker-compose exec file-parsing alembic upgrade head
    docker-compose exec rules-engine alembic upgrade head
    docker-compose exec robotic-integration alembic upgrade head
    ```
- **Kubernetes (Optional**):
  - Create manifests (as previously defined).
  - Apply migrations.
- **Multiple TOMs**:
  - Copy project to `tom1/`, `tom2/`, etc.
  - Set IP alias:
    ```bash
    sudo ip addr add *************/24 dev eth0
    ```
  - Run:
    ```bash
    cd tom2/deployment
    docker-compose up -d
    ```
  - Update Nginx to bind to alias IP.

#### Step 9: Testing

- Write unit tests for each component (as shown in previous steps).
- Test HA failover, file parsing, WebSocket communication, tag management.
