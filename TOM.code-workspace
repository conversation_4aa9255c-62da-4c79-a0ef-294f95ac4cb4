{"folders": [{"path": "docs"}, {"path": "backend"}, {"path": "frontend"}, {"path": "database"}, {"path": "shared"}, {"path": "deployment"}, {"path": "tests"}], "settings": {"python.pythonPath": "./venv/bin/python", "python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "typescript.preferences.importModuleSpecifier": "relative", "eslint.workingDirectories": ["backend/api", "frontend/main-ui", "frontend/board-display"], "editor.formatOnSave": true, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/shared/python"}}, "extensions": {"recommendations": ["ms-python.python", "ms-python.black-formatter", "ms-python.flake8", "ms-python.mypy-type-checker", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode-remote.remote-containers", "ms-azuretools.vscode-docker", "mtxr.sqltools", "mtxr.sqltools-driver-pg", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.test-adapter-converter", "hbenl.vscode-test-explorer", "ryanluker.vscode-coverage-gutters", "foxundermoon.shell-format"]}, "launch": {"version": "0.2.0", "compounds": [{"name": "🚀 Start Full Development Environment", "configurations": [{"folder": "database", "name": "🐳 Start Infrastructure (Postgres + Redis)"}, {"folder": "backend", "name": "🔥 Debug Node.js API (Development Mode)"}, {"folder": "backend", "name": "🪵 Debug Logging Service"}, {"folder": "backend", "name": "⚙️ Debug Settings Service"}, {"folder": "backend", "name": "💾 Debug File Parsing Service"}, {"folder": "backend", "name": "📜 Debug Rules Engine Service"}, {"folder": "backend", "name": "🤖 Debug Robotic Integration Service"}, {"folder": "frontend", "name": "🔥 Start Main UI (Development)"}, {"folder": "frontend", "name": "📺 Start Board Display"}], "stopAll": true, "presentation": {"hidden": false, "group": "full-stack", "order": 1}}, {"name": "🏗️ Start Backend Services Only", "configurations": [{"folder": "database", "name": "🐳 Start Infrastructure (Postgres + Redis)"}, {"folder": "backend", "name": "🔥 Debug Node.js API (Development Mode)"}, {"folder": "backend", "name": "🪵 Debug Logging Service"}, {"folder": "backend", "name": "⚙️ Debug Settings Service"}, {"folder": "backend", "name": "💾 Debug File Parsing Service"}, {"folder": "backend", "name": "📜 Debug Rules Engine Service"}, {"folder": "backend", "name": "🤖 Debug Robotic Integration Service"}], "stopAll": true, "presentation": {"hidden": false, "group": "backend", "order": 2}}, {"name": "🌐 Start Frontend Apps Only", "configurations": [{"folder": "frontend", "name": "🔥 Start Main UI (Development)"}, {"folder": "frontend", "name": "📺 Start Board Display"}], "stopAll": true, "presentation": {"hidden": false, "group": "frontend", "order": 3}}, {"name": "🧪 Debug All Tests", "configurations": [{"folder": "tests", "name": "🧪 Debug All Python Tests"}], "stopAll": true, "presentation": {"hidden": false, "group": "testing", "order": 4}}]}}