# VS Code Multi-Root Workspace Setup for TOM

This document describes the VS Code multi-root workspace configuration for the Task Orchestration Manager (TOM) project.

## 🏗️ Workspace Structure

The project uses a multi-root workspace where each major component has its own `.vscode` folder with specific configurations:

```
TOM/
├── TOM.code-workspace          # Main workspace file with compound launch configs
├── backend/.vscode/            # Backend services configuration
├── frontend/.vscode/           # Frontend applications configuration
├── database/.vscode/           # Database and migration configuration
├── deployment/.vscode/         # Docker and deployment configuration
├── tests/.vscode/              # Testing configuration
└── docs/                       # Documentation (no specific VS Code config)
```

## 🚀 Quick Start

1. **Open the workspace**: Open `TOM.code-workspace` in VS Code
2. **Install recommended extensions**: VS Code will prompt you to install recommended extensions
3. **Setup infrastructure**:
   - Run task `start-postgres` from deployment folder
   - Run task `start-redis` from deployment folder
   - Run task `run-migrations` from database folder
4. **Start debugging**: Use compound launch configurations:
   - "🚀 Start All Backend Services"
   - "🌐 Start Frontend Apps (Development)" - Main UI (HTTP) + Board Display (HTTP)
   - "🔒 Start Frontend Apps (Production-like)" - Main UI (HTTPS) + Board Display (HTTP)
   - "🚀 Start Full Application (HTTP)" - Complete stack with Main UI HTTP
   - "🔒 Start Full Application (HTTPS)" - Complete stack with Main UI HTTPS

## 📁 Workspace Configurations

### Backend Workspace (`backend/.vscode/`)

**Launch Configurations:**

- 🚀 API Server (Node.js/NestJS) - Production build
- 🚀 API Server (Development Mode) - Hot reload with debugging
- 🐍 Python microservices (Logging, Settings, File Parsing, Rules Engine, Robotic Integration)
- 🧪 Test configurations for API and Python services

**Tasks:**

- Build and install dependencies for Node.js and Python services
- Run tests and linting
- Individual service management

**Settings:**

- Python interpreter configuration
- TypeScript/ESLint configuration
- Formatting and linting rules

### Frontend Workspace (`frontend/.vscode/`)

**Launch Configurations:**

- 🌐 Main UI (HTTP - Development) - HTTP development server on port 3000
- 🔒 Main UI (HTTPS - Production-like) - HTTPS development server on port 3000
- 📺 Board Display (HTTP - System Requirement) - HTTP only, no authentication
- 🧪 Test configurations for both frontend apps
- 🔧 Build configurations

**Tasks:**

- Install dependencies for both frontend apps
- Build, test, and lint tasks
- Development server tasks with hot reload

**Settings:**

- TypeScript/JavaScript configuration
- ESLint and Prettier setup
- Tailwind CSS configuration
- SolidJS specific settings

### Database Workspace (`database/.vscode/`)

**Tasks:**

- `run-migrations` - Apply database migrations
- `create-migration` - Create new migration with prompt
- `downgrade-migration` - Rollback last migration
- `migration-history` - View migration history
- `migration-current` - Show current migration

**Settings:**

- Python configuration for Alembic
- SQL Tools connection to development database
- SQL formatting configuration

### Deployment Workspace (`deployment/.vscode/`)

**Tasks:**

- Docker container management (start/stop PostgreSQL and Redis)
- Docker Compose operations (up, down, logs, build)
- Certificate generation
- Deployment scripts
- Health checks

**Settings:**

- Docker and YAML configuration
- Shell script formatting
- File associations for deployment files

### Tests Workspace (`tests/.vscode/`)

**Launch Configurations:**

- 🧪 Run All Python Tests
- 🧪 Run Integration Tests
- 🧪 Run Unit Tests
- 🧪 Run E2E Tests
- 🧪 Debug Current Test File

**Tasks:**

- Test execution with different scopes
- Coverage reporting
- Test database setup and cleanup

**Settings:**

- Python testing configuration
- Coverage reporting setup
- Test-specific environment variables

## 🔧 Compound Launch Configurations

The main workspace file (`TOM.code-workspace`) defines compound launch configurations that can start multiple services across different workspace folders:

### 🚀 Start All Backend Services

Starts all backend microservices:

- API Server (Development Mode)
- Logging Service (port 8001)
- Settings Service (port 8002)
- File Parsing Service (port 8003)
- Rules Engine Service (port 8004)
- Robotic Integration Service (port 8005)

### 🌐 Start Frontend Apps (Development)

Starts both frontend applications in development mode:

- Main UI (http://localhost:3000) - HTTP for development
- Board Display (http://localhost:3001) - HTTP only (system requirement)

### 🔒 Start Frontend Apps (Production-like)

Starts frontend applications in production-like mode:

- Main UI (https://localhost:3000) - HTTPS with authentication
- Board Display (http://localhost:3001) - HTTP only, no authentication

### 🚀 Start Full Application (HTTP)

Starts all backend services and frontend apps with Main UI in HTTP mode.

### 🔒 Start Full Application (HTTPS)

Starts all backend services and frontend apps with Main UI in HTTPS mode for production-like testing.
Board Display always remains HTTP due to system requirements.

### 🧪 Run All Tests

Runs tests across all services and the centralized test suite.

## 🏗️ Mixed HTTP/HTTPS Architecture

The TOM system uses a mixed architecture to meet different system requirements:

### Main UI (Authenticated)

- **Development**: HTTP (http://localhost:3000)
- **Production-like**: HTTPS (https://localhost:3000)
- **Authentication**: Required for all API calls
- **API Endpoint**: Uses HTTPS in production mode

### Board Display (Public)

- **Always HTTP**: http://localhost:3001 (system requirement)
- **No Authentication**: Public access, no auth required
- **API Endpoint**: Uses HTTP endpoints without authentication
- **Reason**: System requirements mandate HTTP-only operation

## 🔒 HTTPS Development Setup

For production-like testing with HTTPS (Main UI only), you'll need SSL certificates:

### Certificate Generation

1. **Generate certificates**: Run the `generate-certificates` task from the deployment workspace
2. **Certificate location**: Certificates will be created in `deployment/certs/`
   - `server.key` - Private key
   - `server.crt` - Certificate file

### HTTPS Launch Configurations

- Use configurations with "🔒" prefix for HTTPS mode (Main UI only)
- Main UI will automatically use HTTPS when certificates are available
- Board Display always remains HTTP regardless of certificates
- API calls from Main UI will be made to `https://localhost` instead of `http://localhost:4000`
- API calls from Board Display always use `http://localhost:4000` without authentication

### Certificate Trust

For development, you may need to:

1. Accept the self-signed certificate in your browser
2. Add the certificate to your system's trusted certificates (optional)

## 🌐 Service URLs

When running the full application:

### HTTP Mode (Development)

- **API Server**: http://localhost:4000
- **API Documentation**: http://localhost:4000/api/docs
- **Main UI**: http://localhost:3000 (with authentication)
- **Board Display**: http://localhost:3001 (no authentication)

### HTTPS Mode (Production-like)

- **API Server**: https://localhost (via Nginx)
- **API Documentation**: https://localhost/api/docs
- **Main UI**: https://localhost:3000 (with authentication)
- **Board Display**: http://localhost:3001 (always HTTP, no authentication)
- **Logging Service**: http://localhost:8001
- **Settings Service**: http://localhost:8002
- **File Parsing Service**: http://localhost:8003
- **Rules Engine Service**: http://localhost:8004
- **Robotic Integration Service**: http://localhost:8005

## 🔍 Debugging

### Multi-Service Debugging

1. Use compound configurations to start multiple services
2. Set breakpoints in any service
3. Debug across service boundaries
4. Each service runs in its own terminal

### Individual Service Debugging

1. Navigate to the appropriate workspace folder
2. Use the specific launch configuration for that service
3. Set breakpoints and debug normally

## 🧪 Testing

### Running Tests

- Use compound configuration "🧪 Run All Tests" to run all tests
- Use individual workspace test configurations for specific services
- Tests run with appropriate environment variables and database configurations

### Test Databases

- Unit tests use SQLite for isolation
- Integration tests use PostgreSQL test database
- E2E tests use full PostgreSQL setup

## 🔧 API Architecture Notes

The backend API needs to support both authenticated and non-authenticated access:

### Authenticated Endpoints (Main UI)

- All standard API endpoints require authentication
- Use JWT tokens for session management
- HTTPS in production mode

### Non-Authenticated Endpoints (Board Display)

- Specific endpoints for board display data
- No authentication required
- HTTP only
- Should be separate endpoints or have optional authentication

### Implementation Considerations

- Board Display endpoints should be designed for public access
- Consider rate limiting for non-authenticated endpoints
- Ensure sensitive data is not exposed through board display endpoints

## 🛠️ Development Workflow

1. **Start Infrastructure**: Use deployment tasks to start PostgreSQL and Redis
2. **Run Migrations**: Use database tasks to set up schema
3. **Start Services**: Use compound launch configurations
4. **Develop**: Edit code with hot reload and debugging
5. **Test**: Run tests using test configurations
6. **Deploy**: Use deployment tasks for Docker operations

## 📝 Environment Variables

Each workspace has appropriate environment files:

- Backend services use `.env` files with local development settings
- Database connections point to localhost:5434 (PostgreSQL) and localhost:6379 (Redis)
- Python services have proper PYTHONPATH configuration
- Frontend apps have API_URL pointing to localhost:4000

## 🔧 Recommended Extensions

The workspace recommends essential extensions:

- Python development (Python, Black, Flake8, MyPy)
- TypeScript/JavaScript development (TypeScript, ESLint, Prettier)
- Frontend development (Tailwind CSS)
- Database tools (SQLTools with PostgreSQL driver)
- Docker tools
- Testing tools (Coverage Gutters, Test Explorer)

## 🚨 Troubleshooting

### Common Issues

1. **Service Won't Start**: Check if required infrastructure (PostgreSQL/Redis) is running
2. **Database Connection**: Verify PostgreSQL is on port 5434 with correct credentials
3. **Python Path Issues**: Check PYTHONPATH in environment variables
4. **Port Conflicts**: Ensure no other services are using the required ports
5. **Dependencies**: Run install tasks for Node.js and Python dependencies

### Debug Tips

- Use the integrated terminal to check service logs
- Verify environment variables in debug console
- Check database connectivity using SQLTools
- Use Docker tasks to manage infrastructure containers
