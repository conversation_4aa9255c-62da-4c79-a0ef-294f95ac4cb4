# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USER=user
DATABASE_PASSWORD=password
DATABASE_NAME=truss
DATABASE_SSL=false
DATABASE_MAX_CONNECTIONS=20
DATABASE_IDLE_TIMEOUT=30000
DATABASE_CONNECTION_TIMEOUT=2000

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=1h

# API Configuration
PORT=4000
NODE_ENV=development
CORS_ORIGIN=*

# Rate Limiting
THROTTLER_TTL=60
THROTTLER_LIMIT=10

# Microservices URLs
LOGGING_SERVICE_URL=http://logging:8000
