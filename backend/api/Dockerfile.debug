# Development Dockerfile for debugging Node.js API
FROM node:18-alpine

# Install debugging tools
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files
COPY backend/api/package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy shared modules
COP<PERSON> shared ./shared

# Copy database models
COPY database ./database

# Copy source code
COPY backend/api .

# Expose application port and debug port
EXPOSE 4000 9229

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:4000/api/health || exit 1

# Start the application in debug mode
CMD ["npm", "run", "start:debug"]
