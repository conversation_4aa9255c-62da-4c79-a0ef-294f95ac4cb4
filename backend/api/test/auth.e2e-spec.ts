import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { DataSource } from 'typeorm';

describe('Authentication (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    dataSource = moduleFixture.get<DataSource>(DataSource);
    
    await app.init();
  });

  afterAll(async () => {
    await dataSource.destroy();
    await app.close();
  });

  describe('/api/auth/users (GET)', () => {
    it('should return list of users', () => {
      return request(app.getHttpServer())
        .get('/api/auth/users')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
          expect(res.body[0]).toHaveProperty('username');
          expect(res.body[0]).toHaveProperty('role');
          expect(res.body[0]).not.toHaveProperty('pin'); // PIN should not be exposed
        });
    });
  });

  describe('/api/auth/login (POST)', () => {
    it('should login with valid credentials', () => {
      return request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          username: 'admin',
          pin: '0000',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('access_token');
          expect(res.body).toHaveProperty('user');
          expect(res.body.user).toHaveProperty('id');
          expect(res.body.user).toHaveProperty('username', 'admin');
          expect(res.body.user).toHaveProperty('role', 'Super Admin');
          expect(res.body.user).toHaveProperty('tenantId');
        });
    });

    it('should reject invalid credentials', () => {
      return request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          username: 'admin',
          pin: 'wrong',
        })
        .expect(401);
    });

    it('should reject missing username', () => {
      return request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          pin: '0000',
        })
        .expect(400);
    });

    it('should reject missing PIN', () => {
      return request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          username: 'admin',
        })
        .expect(400);
    });

    it('should reject PIN that is not 4 digits', () => {
      return request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          username: 'admin',
          pin: '123',
        })
        .expect(400);
    });
  });

  describe('/api/auth/profile (GET)', () => {
    let accessToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          username: 'admin',
          pin: '0000',
        });
      
      accessToken = loginResponse.body.access_token;
    });

    it('should return user profile with valid token', () => {
      return request(app.getHttpServer())
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('sub');
          expect(res.body).toHaveProperty('username', 'admin');
          expect(res.body).toHaveProperty('role', 'Super Admin');
          expect(res.body).toHaveProperty('tenantId');
        });
    });

    it('should reject request without token', () => {
      return request(app.getHttpServer())
        .get('/api/auth/profile')
        .expect(401);
    });

    it('should reject request with invalid token', () => {
      return request(app.getHttpServer())
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('/api/auth/logout (POST)', () => {
    let accessToken: string;

    beforeEach(async () => {
      const loginResponse = await request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          username: 'admin',
          pin: '0000',
        });
      
      accessToken = loginResponse.body.access_token;
    });

    it('should logout successfully with valid token', () => {
      return request(app.getHttpServer())
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('message', 'Logged out successfully');
        });
    });

    it('should reject logout without token', () => {
      return request(app.getHttpServer())
        .post('/api/auth/logout')
        .expect(401);
    });
  });
});
