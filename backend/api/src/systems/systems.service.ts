import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { System } from "../entities/system.entity";

@Injectable()
export class SystemsService {
  constructor(
    @InjectRepository(System)
    private systemsRepository: Repository<System>
  ) {}

  async findAll(): Promise<System[]> {
    return this.systemsRepository.find();
  }

  async findOne(id: number): Promise<System> {
    return this.systemsRepository.findOne({
      where: { id },
      relations: ["users"],
    });
  }
}
