import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { SystemsService } from "./systems.service";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";

@ApiTags("systems")
@Controller("systems")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SystemsController {
  constructor(private readonly systemsService: SystemsService) {}

  @Get()
  @ApiOperation({ summary: "Get all systems" })
  @ApiResponse({ status: 200, description: "Systems retrieved successfully" })
  findAll() {
    return this.systemsService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get system by ID" })
  @ApiResponse({ status: 200, description: "System retrieved successfully" })
  findOne(@Param("id") id: string) {
    return this.systemsService.findOne(+id);
  }
}
