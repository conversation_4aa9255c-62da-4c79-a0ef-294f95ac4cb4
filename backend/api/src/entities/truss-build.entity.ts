import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
} from "typeorm";
import { System } from "./system.entity";
import { Truss } from "./truss.entity";

@Entity("truss_builds")
export class TrussBuild {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: number;

  @Column({ name: "truss_id", type: "bigint" })
  trussId: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ name: "build_timestamp", type: "timestamp" })
  buildTimestamp: Date;

  @Column({ name: "boards_snapshot", type: "jsonb", nullable: false })
  boardsSnapshot: any[];

  @Column({ name: "plates_snapshot", type: "jsonb", nullable: false })
  platesSnapshot: any[];

  @Column({ name: "robot_data", type: "jsonb", nullable: false })
  robotData: any;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => Truss, (truss) => truss.builds)
  @JoinColumn({ name: "truss_id" })
  truss: Truss;

  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;
}
