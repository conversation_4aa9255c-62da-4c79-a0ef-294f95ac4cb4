import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { System } from "./system.entity";
import { Truss } from "./truss.entity";

@Entity("plates")
export class Plate {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: number;

  @Column({ name: "truss_id", type: "bigint" })
  trussId: number;

  @Column({ name: "system_id" })
  systemId: number;

  // Plate identification
  @Column({ name: "unique_id", length: 100, nullable: true })
  uniqueId: string;

  @Column({ type: "float", nullable: true })
  gauge: number;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({ name: "plate_height", type: "float", nullable: true })
  plateHeight: number;

  @Column({ name: "plate_width", type: "float", nullable: true })
  plateWidth: number;

  // Geometry
  @Column({ type: "jsonb", nullable: false })
  vertices: any;

  @Column({ name: "original_vertices", type: "jsonb", nullable: false })
  originalVertices: any;

  @Column({ length: 100, nullable: true })
  label: string;

  // Position and orientation
  @Column({ type: "jsonb", nullable: false })
  offset: any;

  @Column({ name: "original_offset", type: "jsonb", nullable: false })
  originalOffset: any;

  @Column({ name: "center_point", type: "jsonb", nullable: false })
  centerPoint: any;

  @Column({ name: "original_center_point", type: "jsonb", nullable: false })
  originalCenterPoint: any;

  @Column({ name: "object_guid", length: 36, nullable: true })
  objectGuid: string;

  @Column({ type: "jsonb", nullable: false })
  dimensions: any;

  @Column({ name: "original_dimensions", type: "jsonb", nullable: false })
  originalDimensions: any;

  @Column({ type: "jsonb", nullable: false })
  rotation: any;

  @Column({ name: "original_rotation", type: "jsonb", nullable: false })
  originalRotation: any;

  // File-specific data
  @Column({ name: "file_center_point", type: "jsonb", nullable: true })
  fileCenterPoint: any;

  @Column({ name: "file_rotation", type: "jsonb", nullable: true })
  fileRotation: any;

  @Column({ name: "file_vertices", type: "jsonb", nullable: true })
  fileVertices: any;

  // Manufacturing properties
  @Column({ default: false, nullable: false })
  manual: boolean;

  // Modification tracking
  @Column({ type: "jsonb", default: () => "'[]'" })
  modifications: any[];

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "modified_at" })
  modifiedAt: Date;

  // Relationships
  @ManyToOne(() => Truss, (truss) => truss.plates)
  @JoinColumn({ name: "truss_id" })
  truss: Truss;

  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;
}
