import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { System } from "./system.entity";
import { Truss } from "./truss.entity";

@Entity("boards")
export class Board {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: number;

  @Column({ name: "truss_id", type: "bigint" })
  trussId: number;

  @Column({ name: "system_id" })
  systemId: number;

  // Board identification
  @Column({ name: "piece_type", length: 50, nullable: true })
  pieceType: string;

  @Column({ length: 100, nullable: true })
  label: string;

  @Column({ length: 100, nullable: true })
  name: string;

  @Column({ name: "object_guid", length: 36, nullable: true })
  objectGuid: string;

  // Dimensions (stored as JSON for flexibility)
  @Column({ type: "jsonb", nullable: false })
  dimensions: any;

  @Column({ name: "original_dimensions", type: "jsonb", nullable: false })
  originalDimensions: any;

  // Geometry
  @Column({ type: "jsonb", nullable: false })
  vertices: any;

  @Column({ name: "original_vertices", type: "jsonb", nullable: false })
  originalVertices: any;

  // Material properties
  @Column({ name: "member_grade", length: 50, nullable: true })
  memberGrade: string;

  @Column({ name: "member_species", length: 50, nullable: true })
  memberSpecies: string;

  @Column({ type: "float", nullable: true })
  weight: number;

  // Manufacturing properties
  @Column({ default: false, nullable: false })
  manual: boolean;

  @Column({ type: "jsonb", nullable: true })
  direction: any;

  @Column({ name: "place_location", type: "jsonb", nullable: true })
  placeLocation: any;

  @Column({ name: "plate_edges", type: "jsonb", nullable: true })
  plateEdges: any;

  @Column({ name: "pick_offset", type: "jsonb", nullable: true })
  pickOffset: any;

  @Column({ type: "float", nullable: true })
  angle: number;

  @Column({ name: "angle_limits", type: "jsonb", nullable: true })
  angleLimits: any;

  // Plate relationships
  @Column({ name: "attached_plates", type: "jsonb", nullable: true })
  attachedPlates: number[];

  @Column({ name: "intersecting_plates", type: "jsonb", nullable: true })
  intersectingPlates: number[];

  @Column({ name: "splice_plate_id", type: "bigint", nullable: true })
  splicePlateId: number;

  @Column({ name: "center_plate_ids", type: "jsonb", nullable: true })
  centerPlateIds: number[];

  // File-specific data
  @Column({ name: "file_vertices", type: "jsonb", nullable: true })
  fileVertices: any;

  @Column({ name: "file_rotation", type: "jsonb", nullable: true })
  fileRotation: any;

  @Column({ name: "file_order", nullable: true })
  fileOrder: number;

  // Labels and notes
  @Column({ name: "print_label", length: 100, nullable: true })
  printLabel: string;

  @Column({ type: "text", nullable: true })
  notes: string;

  // Modification tracking
  @Column({ type: "jsonb", default: () => "'[]'" })
  modifications: any[];

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "modified_at" })
  modifiedAt: Date;

  // Relationships
  @ManyToOne(() => Truss, (truss) => truss.boards)
  @JoinColumn({ name: "truss_id" })
  truss: Truss;

  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;
}
