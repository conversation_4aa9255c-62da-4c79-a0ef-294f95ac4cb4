import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import { System } from "./system.entity";

@Entity("users")
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ length: 100, nullable: false })
  username: string;

  @Column({ length: 4, nullable: false })
  pin: string;

  @Column({ length: 50, nullable: false })
  role: string; // Super Admin, Site Admin, User, Viewer

  // Relationships
  @ManyToOne(() => System, (system) => system.users)
  @JoinColumn({ name: "system_id" })
  system: System;
}
