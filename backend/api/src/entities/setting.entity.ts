import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { System } from "./system.entity";

@Entity("settings")
export class Setting {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ length: 100, nullable: false })
  key: string;

  @Column({ type: "jsonb", nullable: false })
  value: any;

  @Column({ nullable: false, default: 1 })
  version: number;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "modified_at" })
  modifiedAt: Date;

  // Relationships
  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;
}
