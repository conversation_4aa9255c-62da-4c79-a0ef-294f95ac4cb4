import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  <PERSON><PERSON>C<PERSON>umn,
  CreateDateColumn,
} from "typeorm";
import { System } from "./system.entity";

@Entity("logs")
export class Log {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ name: "user_id", nullable: true })
  userId: number;

  @Column({ length: 50, nullable: true })
  role: string;

  @Column({ name: "action_type", length: 50 })
  actionType: string;

  @Column({ name: "target_data", type: "jsonb", nullable: true })
  targetData: any;

  @CreateDateColumn({ name: "timestamp" })
  timestamp: Date;

  // Relationships
  @ManyToOne(() => System, (system) => system.logs)
  @JoinColumn({ name: "system_id" })
  system: System;
}
