import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import { Tag } from "./tag.entity";

export enum EntityType {
  BATCH = "batch",
}

@Entity("tags_relations")
export class TagsRelation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: "enum",
    enum: EntityType,
    nullable: false,
  })
  entityType: EntityType;

  @Column({ name: "type_id", type: "bigint", nullable: false })
  typeId: number;

  @Column({ name: "tag_id", nullable: false })
  tagId: number;

  // Relationships
  @ManyToOne(() => Tag, (tag) => tag.tagsRelations)
  @JoinColumn({ name: "tag_id" })
  tag: Tag;
}
