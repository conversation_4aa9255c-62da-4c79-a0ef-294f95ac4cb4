import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { System } from "./system.entity";
import { Truss } from "./truss.entity";

@Entity("batches")
export class Batch {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ length: 100, nullable: false })
  name: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({ length: 50, nullable: false })
  status: string;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "modified_at" })
  modifiedAt: Date;

  // Relationships
  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;

  @OneToMany(() => Truss, (truss) => truss.batch)
  trusses: Truss[];
}
