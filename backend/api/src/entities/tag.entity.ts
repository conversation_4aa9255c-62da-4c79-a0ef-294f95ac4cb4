import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  OneToMany,
} from "typeorm";
import { TagsRelation } from "./tags-relation.entity";

@Entity("tags")
export class Tag {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100, nullable: false, unique: true })
  name: string;

  // Relationships
  @OneToMany(() => TagsRelation, (tagsRelation) => tagsRelation.tag)
  tagsRelations: TagsRelation[];
}
