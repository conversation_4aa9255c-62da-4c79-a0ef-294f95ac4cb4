import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from "typeorm";
import { System } from "./system.entity";

@Entity("truss_files")
@Index("IDX_truss_files_system_id", ["systemId", "id"])
export class TrussFile {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ type: "bytea", nullable: false })
  fileData: Buffer;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;
}
