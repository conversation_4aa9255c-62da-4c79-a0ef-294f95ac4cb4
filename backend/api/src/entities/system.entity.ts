import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, OneToMany } from "typeorm";
import { User } from "./user.entity";
import { Log } from "./log.entity";

@Entity("systems")
export class System {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100, nullable: false })
  name: string;

  // Relationships
  @OneToMany(() => User, (user) => user.system)
  users: User[];

  @OneToMany(() => Log, (log) => log.system)
  logs: Log[];
}
