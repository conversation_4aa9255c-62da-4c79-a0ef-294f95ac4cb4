import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { System } from "./system.entity";
import { Batch } from "./batch.entity";
import { Board } from "./board.entity";
import { Plate } from "./plate.entity";
import { TrussBuild } from "./truss-build.entity";

@Entity("trusses")
export class Truss {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: number;

  @Column({ name: "batch_id", type: "bigint" })
  batchId: number;

  @Column({ name: "system_id" })
  systemId: number;

  @Column({ type: "jsonb", nullable: true })
  metadata: any;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "modified_at" })
  modifiedAt: Date;

  // Relationships
  @ManyToOne(() => Batch, (batch) => batch.trusses)
  @JoinColumn({ name: "batch_id" })
  batch: Batch;

  @ManyToOne(() => System)
  @JoinColumn({ name: "system_id" })
  system: System;

  @OneToMany(() => Board, (board) => board.truss)
  boards: Board[];

  @OneToMany(() => Plate, (plate) => plate.truss)
  plates: Plate[];

  @OneToMany(() => TrussBuild, (build) => build.truss)
  builds: TrussBuild[];
}
