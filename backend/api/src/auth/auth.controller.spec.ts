import { Test, TestingModule } from "@nestjs/testing";
import { JwtService } from "@nestjs/jwt";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { LogsService } from "../logs/logs.service";

describe("AuthController", () => {
  let controller: AuthController;
  let authService: AuthService;
  let logsService: LogsService;

  const mockUser = {
    id: 1,
    username: "testuser",
    role: "User",
    systemId: 1,
    pin: "1234",
  };

  const mockAuthService = {
    login: jest.fn(),
    logout: jest.fn(),
    getAllUsers: jest.fn(),
  };

  const mockLogsService = {
    createLog: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: LogsService,
          useValue: mockLogsService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    logsService = module.get<LogsService>(LogsService);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("login", () => {
    it("should login successfully and create log entry", async () => {
      const loginDto = { username: "testuser", pin: "1234" };
      const loginResult = {
        access_token: "jwt-token",
        user: mockUser,
      };

      mockAuthService.login.mockResolvedValue(loginResult);
      mockLogsService.createLog.mockResolvedValue(undefined);

      const result = await controller.login(loginDto);

      expect(result).toEqual(loginResult);
      expect(authService.login).toHaveBeenCalledWith(loginDto);
      expect(logsService.createLog).toHaveBeenCalledWith({
        systemId: mockUser.systemId,
        userId: mockUser.id,
        role: mockUser.role,
        actionType: "login",
        targetData: { username: loginDto.username },
      });
    });

    it("should throw error for invalid credentials", async () => {
      const loginDto = { username: "testuser", pin: "wrong" };

      mockAuthService.login.mockRejectedValue(new Error("Invalid credentials"));

      await expect(controller.login(loginDto)).rejects.toThrow(
        "Invalid credentials"
      );
    });
  });

  describe("logout", () => {
    it("should logout successfully and create log entry", async () => {
      const req = {
        user: {
          sub: mockUser.id,
          username: mockUser.username,
          role: mockUser.role,
          systemId: mockUser.systemId,
        },
      };

      mockAuthService.logout.mockResolvedValue({
        message: "Logged out successfully",
      });
      mockLogsService.createLog.mockResolvedValue(undefined);

      const result = await controller.logout(req);

      expect(result).toEqual({ message: "Logged out successfully" });
      expect(authService.logout).toHaveBeenCalledWith(req.user.sub);
      expect(logsService.createLog).toHaveBeenCalledWith({
        systemId: req.user.systemId,
        userId: req.user.sub,
        role: req.user.role,
        actionType: "logout",
        targetData: { username: req.user.username },
      });
    });
  });

  describe("getUsers", () => {
    it("should return list of users", async () => {
      const users = [mockUser];
      mockAuthService.getAllUsers.mockResolvedValue(users);

      const result = await controller.getUsers();

      expect(result).toEqual(users);
      expect(authService.getAllUsers).toHaveBeenCalled();
    });
  });

  describe("getProfile", () => {
    it("should return user profile", () => {
      const req = {
        user: {
          sub: mockUser.id,
          username: mockUser.username,
          role: mockUser.role,
          systemId: mockUser.systemId,
        },
      };

      const result = controller.getProfile(req);

      expect(result).toEqual(req.user);
    });
  });
});
