import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { PassportModule } from "@nestjs/passport";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { JwtStrategy } from "./jwt.strategy";
import { RolesGuard } from "./roles.guard";
import { User } from "../entities/user.entity";
import { RedisModule } from "../redis/redis.module";
import { LogsModule } from "../logs/logs.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    PassportModule,
    RedisModule,
    LogsModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, RolesGuard],
  exports: [AuthService, RolesGuard],
})
export class AuthModule {}
