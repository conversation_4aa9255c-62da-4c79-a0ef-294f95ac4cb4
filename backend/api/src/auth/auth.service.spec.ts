import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { JwtService } from "@nestjs/jwt";
import { Repository } from "typeorm";
import { AuthService } from "./auth.service";
import { User } from "../entities/user.entity";
import { RedisService } from "../redis/redis.service";

describe("AuthService", () => {
  let service: AuthService;
  let userRepository: Repository<User>;
  let jwtService: JwtService;
  let redisService: RedisService;

  const mockUser = {
    id: 1,
    username: "testuser",
    pin: "1234",
    role: "User",
    systemId: 1,
    system: { id: 1, name: "Test System" },
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockRedisService = {
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    jwtService = module.get<JwtService>(JwtService);
    redisService = module.get<RedisService>(RedisService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("validateUser", () => {
    it("should return user when credentials are valid", async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.validateUser("testuser", "1234");

      expect(result).toEqual(mockUser);
      expect(userRepository.findOne).toHaveBeenCalledWith({
        where: { username: "testuser", pin: "1234" },
        relations: ["system"],
      });
    });

    it("should return null when credentials are invalid", async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.validateUser("testuser", "wrong");

      expect(result).toBeNull();
    });
  });

  describe("login", () => {
    it("should return access token and user data", async () => {
      const loginDto = { username: "testuser", pin: "1234" };
      const token = "jwt-token";

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockJwtService.sign.mockReturnValue(token);
      mockRedisService.set.mockResolvedValue(undefined);

      const result = await service.login(loginDto);

      expect(result).toEqual({
        access_token: token,
        user: {
          id: mockUser.id,
          username: mockUser.username,
          role: mockUser.role,
          systemId: mockUser.systemId,
        },
      });

      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: mockUser.id,
        username: mockUser.username,
        role: mockUser.role,
        systemId: mockUser.systemId,
      });

      expect(redisService.set).toHaveBeenCalledWith(
        `session:${mockUser.id}`,
        token,
        3600
      );
    });

    it("should throw error for invalid credentials", async () => {
      const loginDto = { username: "testuser", pin: "wrong" };

      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.login(loginDto)).rejects.toThrow(
        "Invalid credentials"
      );
    });
  });

  describe("logout", () => {
    it("should remove session from Redis", async () => {
      mockRedisService.del.mockResolvedValue(undefined);

      const result = await service.logout(1);

      expect(result).toEqual({ message: "Logged out successfully" });
      expect(redisService.del).toHaveBeenCalledWith("session:1");
    });
  });

  describe("validateToken", () => {
    it("should return true for valid token", async () => {
      const token = "valid-token";
      const payload = { sub: 1 };

      mockJwtService.verify.mockReturnValue(payload);
      mockRedisService.get.mockResolvedValue(token);

      const result = await service.validateToken(token);

      expect(result).toBe(true);
    });

    it("should return false for invalid token", async () => {
      const token = "invalid-token";

      mockJwtService.verify.mockImplementation(() => {
        throw new Error("Invalid token");
      });

      const result = await service.validateToken(token);

      expect(result).toBe(false);
    });
  });

  describe("getAllUsers", () => {
    it("should return list of users", async () => {
      const users = [mockUser];
      mockUserRepository.find.mockResolvedValue(users);

      const result = await service.getAllUsers();

      expect(result).toEqual(users);
      expect(userRepository.find).toHaveBeenCalledWith({
        relations: ["system"],
        select: ["id", "username", "role", "systemId"],
      });
    });
  });
});
