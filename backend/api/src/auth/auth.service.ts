import { Injectable, UnauthorizedException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { JwtService } from "@nestjs/jwt";
import { User } from "../entities/user.entity";
import { RedisService } from "../redis/redis.service";
import { LoginDto } from "./dto/login.dto";

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private jwtService: JwtService,
    private redisService: RedisService
  ) {}

  async validateUser(username: string, pin: string): Promise<User | null> {
    const user = await this.usersRepository.findOne({
      where: { username, pin },
      relations: ["system"],
    });

    if (user) {
      return user;
    }
    return null;
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.username, loginDto.pin);

    if (!user) {
      throw new UnauthorizedException("Invalid credentials");
    }

    const payload = {
      sub: user.id,
      username: user.username,
      role: user.role,
      systemId: user.systemId,
    };

    const token = this.jwtService.sign(payload);

    // Store session in Redis
    await this.redisService.set(`session:${user.id}`, token, 3600); // 1 hour

    return {
      access_token: token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        systemId: user.systemId,
      },
    };
  }

  async logout(userId: number) {
    await this.redisService.del(`session:${userId}`);
    return { message: "Logged out successfully" };
  }

  async validateToken(token: string): Promise<boolean> {
    try {
      const payload = this.jwtService.verify(token);
      const storedToken = await this.redisService.get(`session:${payload.sub}`);
      return storedToken === token;
    } catch {
      return false;
    }
  }

  async getAllUsers(): Promise<User[]> {
    return this.usersRepository.find({
      relations: ["system"],
      select: ["id", "username", "role", "systemId"],
    });
  }
}
