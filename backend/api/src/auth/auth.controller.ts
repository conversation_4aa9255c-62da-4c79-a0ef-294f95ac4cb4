import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { AuthService } from "./auth.service";
import { JwtAuthGuard } from "./jwt-auth.guard";
import { LoginDto } from "./dto/login.dto";
import { LogsService } from "../logs/logs.service";

@ApiTags("auth")
@Controller("auth")
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly logsService: LogsService
  ) {}

  @Post("login")
  @ApiOperation({ summary: "User login with PIN" })
  @ApiResponse({ status: 200, description: "Login successful" })
  @ApiResponse({ status: 401, description: "Invalid credentials" })
  async login(@Body() loginDto: LoginDto) {
    try {
      const result = await this.authService.login(loginDto);

      // Log the login action
      await this.logsService.createLog({
        systemId: result.user.systemId,
        userId: result.user.id,
        role: result.user.role,
        actionType: "login",
        targetData: { username: loginDto.username },
      });

      return result;
    } catch (error) {
      throw new HttpException("Invalid credentials", HttpStatus.UNAUTHORIZED);
    }
  }

  @Post("logout")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "User logout" })
  @ApiResponse({ status: 200, description: "Logout successful" })
  async logout(@Request() req) {
    const result = await this.authService.logout(req.user.sub);

    // Log the logout action
    await this.logsService.createLog({
      systemId: req.user.systemId,
      userId: req.user.sub,
      role: req.user.role,
      actionType: "logout",
      targetData: { username: req.user.username },
    });

    return result;
  }

  @Get("users")
  @ApiOperation({ summary: "Get all users for login dropdown" })
  @ApiResponse({ status: 200, description: "Users retrieved successfully" })
  async getUsers() {
    return this.authService.getAllUsers();
  }

  @Get("profile")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get current user profile" })
  @ApiResponse({ status: 200, description: "Profile retrieved successfully" })
  getProfile(@Request() req) {
    return req.user;
  }
}
