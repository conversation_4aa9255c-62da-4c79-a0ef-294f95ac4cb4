import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { JwtModule } from "@nestjs/jwt";
import { ThrottlerModule } from "@nestjs/throttler";
import { HttpModule } from "@nestjs/axios";
import { AuthModule } from "./auth/auth.module";
import { UsersModule } from "./users/users.module";
import { SystemsModule } from "./systems/systems.module";
import { LogsModule } from "./logs/logs.module";
import { HealthModule } from "./health/health.module";
import { RedisModule } from "./redis/redis.module";
import { DatabaseModule } from "./database/database.module";

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.local", ".env"],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: "postgres",
        host: configService.get("DATABASE_HOST", "postgres"),
        port: configService.get("DATABASE_PORT", 5432),
        username: configService.get("DATABASE_USER", "user"),
        password: configService.get("DATABASE_PASSWORD", "password"),
        database: configService.get("DATABASE_NAME", "tom"),
        entities: [__dirname + "/**/*.entity{.ts,.js}"],
        synchronize: false, // Use migrations instead
        logging: configService.get("NODE_ENV") === "development",
        ssl: false,
        extra: {
          max: configService.get("DATABASE_MAX_CONNECTIONS", 20),
          idleTimeoutMillis: configService.get("DATABASE_IDLE_TIMEOUT", 30000),
          connectionTimeoutMillis: configService.get(
            "DATABASE_CONNECTION_TIMEOUT",
            2000
          ),
        },
      }),
      inject: [ConfigService],
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get("THROTTLER_TTL", 60),
        limit: configService.get("THROTTLER_LIMIT", 10),
      }),
      inject: [ConfigService],
    }),

    // JWT
    JwtModule.registerAsync({
      global: true,
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get("JWT_SECRET", "your-secret-key"),
        signOptions: {
          expiresIn: configService.get("JWT_EXPIRES_IN", "1h"),
        },
      }),
      inject: [ConfigService],
    }),

    // Common modules
    DatabaseModule,
    RedisModule,
    HealthModule,

    // Feature modules
    AuthModule,
    UsersModule,
    SystemsModule,
    LogsModule,
  ],
})
export class AppModule {}
