import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1700000000000 implements MigrationInterface {
  name = "InitialSchema1700000000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create systems table
    await queryRunner.query(`
      CREATE TABLE "systems" (
        "id" SERIAL NOT NULL,
        "name" character varying(100) NOT NULL,
        CONSTRAINT "PK_systems_id" PRIMARY KEY ("id")
      )
    `);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" SERIAL NOT NULL,
        "system_id" integer NOT NULL,
        "username" character varying(100) NOT NULL,
        "pin" character varying(4) NOT NULL,
        "role" character varying(50) NOT NULL,
        CONSTRAINT "PK_users_id" PRIMARY KEY ("id"),
        CONSTRAINT "FK_users_system_id" FOREIGN KEY ("system_id") REFERENCES "systems"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
      )
    `);

    // Create logs table
    await queryRunner.query(`
      CREATE TABLE "logs" (
        "id" SERIAL NOT NULL,
        "system_id" integer NOT NULL,
        "user_id" integer,
        "role" character varying(50),
        "action_type" character varying(50) NOT NULL,
        "target_data" jsonb,
        "timestamp" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_logs_id" PRIMARY KEY ("id"),
        CONSTRAINT "FK_logs_system_id" FOREIGN KEY ("system_id") REFERENCES "systems"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
      )
    `);

    // Create indexes
    await queryRunner.query(
      `CREATE INDEX "IDX_users_system_id" ON "users" ("system_id")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_users_username" ON "users" ("username")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_logs_system_id" ON "logs" ("system_id")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_logs_user_id" ON "logs" ("user_id")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_logs_action_type" ON "logs" ("action_type")`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_logs_timestamp" ON "logs" ("timestamp")`
    );

    // Insert default system
    await queryRunner.query(`
      INSERT INTO "systems" ("id", "name") VALUES (1, 'Default System')
      ON CONFLICT ("id") DO NOTHING
    `);

    // Insert default admin user
    await queryRunner.query(`
      INSERT INTO "users" ("id", "system_id", "username", "pin", "role")
      VALUES (1, 1, 'admin', '0000', 'Super Admin')
      ON CONFLICT ("id") DO NOTHING
    `);

    // Insert additional test users
    await queryRunner.query(`
      INSERT INTO "users" ("id", "system_id", "username", "pin", "role") VALUES
      (2, 1, 'operator1', '1234', 'User'),
      (3, 1, 'supervisor', '5678', 'Site Admin'),
      (4, 1, 'viewer', '9999', 'Viewer')
      ON CONFLICT ("id") DO NOTHING
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "IDX_logs_timestamp"`);
    await queryRunner.query(`DROP INDEX "IDX_logs_action_type"`);
    await queryRunner.query(`DROP INDEX "IDX_logs_user_id"`);
    await queryRunner.query(`DROP INDEX "IDX_logs_system_id"`);
    await queryRunner.query(`DROP INDEX "IDX_users_username"`);
    await queryRunner.query(`DROP INDEX "IDX_users_system_id"`);
    await queryRunner.query(`DROP TABLE "logs"`);
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TABLE "systems"`);
  }
}
