import { DataSource } from "typeorm";
import { ConfigService } from "@nestjs/config";
import { config } from "dotenv";

// Load environment variables
config();

const configService = new ConfigService();

export const AppDataSource = new DataSource({
  type: "postgres",
  host: configService.get("DATABASE_HOST", "postgres"),
  port: configService.get("DATABASE_PORT", 5432),
  username: configService.get("DATABASE_USER", "user"),
  password: configService.get("DATABASE_PASSWORD", "password"),
  database: configService.get("DATABASE_NAME", "tom"),
  entities: [__dirname + "/../entities/*.entity{.ts,.js}"],
  migrations: [__dirname + "/../migrations/*{.ts,.js}"],
  synchronize: false,
  logging: configService.get("NODE_ENV") === "development",
  ssl: configService.get("DATABASE_SSL", false),
});
