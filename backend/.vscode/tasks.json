{"version": "2.0.0", "tasks": [{"label": "build-api", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/api"}, "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🔧 Install API Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/api"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "dependencies"}}, {"label": "🐍 Install Python Dependencies (All Services)", "type": "shell", "command": "bash", "args": ["-c", "for service in logging settings file-parsing rules-engine robotic-integration; do echo \"Installing dependencies for $service...\"; pip install -r $service/requirements.txt; done"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "dependencies"}}, {"label": "🧪 Test API (Jest)", "type": "shell", "command": "npm", "args": ["test"], "options": {"cwd": "${workspaceFolder}/api"}, "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🧪 Test Python Services (Pytest)", "type": "shell", "command": "bash", "args": ["-c", "for service in logging settings file-parsing rules-engine robotic-integration; do echo \"Testing $service...\"; cd $service && python -m pytest -v && cd ..; done"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🔍 Lint API (ESLint)", "type": "shell", "command": "npm", "args": ["run", "lint"], "options": {"cwd": "${workspaceFolder}/api"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "linting"}, "problemMatcher": ["$eslint-stylish"]}]}