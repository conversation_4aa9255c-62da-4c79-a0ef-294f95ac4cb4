{"version": "0.2.0", "configurations": [{"name": "🚀 Debug Node.js API", "type": "node", "request": "launch", "program": "${workspaceFolder}/api/dist/main.js", "preLaunchTask": "build-api", "cwd": "${workspaceFolder}/api", "envFile": "${workspaceRoot}/.env", "env": {"NODE_ENV": "development", "DATABASE_HOST": "localhost", "DATABASE_PORT": "5442", "DATABASE_USER": "user", "DATABASE_PASSWORD": "password", "DATABASE_NAME": "tom", "REDIS_URL": "redis://localhost:6389", "JWT_SECRET": "dev-secret-key", "PORT": "4010"}, "console": "integratedTerminal", "restart": true, "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/api/dist/**/*.js"], "presentation": {"hidden": false, "group": "backend", "order": 1}}, {"name": "🔥 Debug Node.js API (Development Mode)", "type": "node", "request": "launch", "program": "${workspaceFolder}/api/node_modules/@nestjs/cli/bin/nest.js", "args": ["start", "--watch", "--debug"], "cwd": "${workspaceFolder}/api", "envFile": "${workspaceRoot}/.env", "env": {"NODE_ENV": "development", "DATABASE_HOST": "localhost", "DATABASE_PORT": "5442", "DATABASE_USER": "user", "DATABASE_PASSWORD": "password", "DATABASE_NAME": "tom", "REDIS_URL": "redis://localhost:6389", "JWT_SECRET": "dev-secret-key", "PORT": "4010"}, "console": "integratedTerminal", "restart": true, "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "presentation": {"hidden": false, "group": "backend", "order": 2}}, {"name": "🪵 Debug Logging Service", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/logging/main.py", "cwd": "${workspaceFolder}/logging", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python:${workspaceFolder}/logging", "UVICORN_HOST": "0.0.0.0", "UVICORN_PORT": "8011"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "python-services", "order": 1}}, {"name": "⚙️ Debug Settings Service", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/settings/main.py", "cwd": "${workspaceFolder}/settings", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python:${workspaceFolder}/settings", "UVICORN_HOST": "0.0.0.0", "UVICORN_PORT": "8012"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "python-services", "order": 2}}, {"name": "💾 Debug File Parsing Service", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/file-parsing/main.py", "cwd": "${workspaceFolder}/file-parsing", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python:${workspaceFolder}/file-parsing", "UVICORN_HOST": "0.0.0.0", "UVICORN_PORT": "8013"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "python-services", "order": 3}}, {"name": "📜 Debug Rules Engine Service", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/rules-engine/main.py", "cwd": "${workspaceFolder}/rules-engine", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python:${workspaceFolder}/rules-engine", "UVICORN_HOST": "0.0.0.0", "UVICORN_PORT": "8014"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "python-services", "order": 4}}, {"name": "🤖 Debug Robotic Integration Service", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/robotic-integration/main.py", "cwd": "${workspaceFolder}/robotic-integration", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python:${workspaceFolder}/robotic-integration", "UVICORN_HOST": "0.0.0.0", "UVICORN_PORT": "8015"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "python-services", "order": 5}}]}