import os
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

# Optional debugging support
DEBUG_ENABLED = os.getenv("DEBUG_ENABLED", "false").lower() == "true"
DEBUG_PORT = int(os.getenv("DEBUG_SETTINGS_PORT", "5679"))

if DEBUG_ENABLED:
    try:
        import debugpy

        debugpy.listen(("0.0.0.0", DEBUG_PORT))
        print(f"🐛 Settings Service: Debug server listening on port {DEBUG_PORT}")
        print("🐛 Waiting for debugger to attach...")
        debugpy.wait_for_client()
        print("🐛 Debugger attached!")
    except ImportError:
        print("⚠️ debugpy not installed, skipping debug setup")
    except Exception as e:
        print(f"⚠️ Debug setup failed: {e}")

app = FastAPI(
    title="Settings Service",
    description="TOM Settings Management Service",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    return {"message": "Settings Service is running"}


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "settings"}


@app.get("/settings")
async def get_settings():
    # TODO: Implement settings retrieval
    return {"settings": {}}


@app.post("/settings")
async def update_settings(settings: dict):
    # TODO: Implement settings update
    return {"message": "Settings updated", "settings": settings}


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("SETTINGS_SERVICE_PORT", "8002"))
    uvicorn.run(app, host="0.0.0.0", port=port, reload=True)
