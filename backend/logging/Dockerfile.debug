# Development Dockerfile for debugging Python Logging Service
FROM python:3.11-slim

# Install system dependencies and debugging tools
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements
COPY backend/logging/requirements.txt .

# Install Python dependencies including debugging tools
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir debugpy

# Copy shared modules
COPY shared /app/shared

# Copy database models
COPY database /app/database

# Copy application code
COPY backend/logging /app/

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser \
    && chown -R appuser:appuser /app
USER appuser

# Expose application port and debug port
EXPOSE 8000 5678

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start the application in debug mode
CMD ["python", "-m", "debugpy", "--listen", "0.0.0.0:5678", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
