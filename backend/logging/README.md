# Truss Logging Microservice

The Logging Microservice is responsible for recording and retrieving audit logs, errors, and actions within the Truss Robotic Management System. It provides a centralized logging solution that stores all system activities in PostgreSQL.

## Features

- **Log Creation**: Record actions, errors, and system events
- **Log Retrieval**: Query logs with filtering and pagination
- **Multi-tenant Support**: Tenant-isolated logging
- **RESTful API**: FastAPI-based REST endpoints
- **Health Monitoring**: Built-in health check endpoint
- **Comprehensive Testing**: Unit and integration tests

## API Endpoints

### Health Check
- `GET /health` - Health check endpoint

### Log Management
- `POST /log` - Create a new log entry
- `GET /logs` - Retrieve logs with filtering and pagination
- `GET /logs/{log_id}` - Retrieve a specific log entry

## API Documentation

When running the service, you can access:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Data Model

### Log Entry
```json
{
  "id": 1,
  "tenant_id": 1,
  "user_id": 123,
  "role": "User",
  "action_type": "create_batch",
  "target_data": {
    "batch_id": 456,
    "name": "Batch ABC"
  },
  "timestamp": "2025-07-10T12:00:00Z"
}
```

## Development

### Prerequisites
- Python 3.11+
- PostgreSQL (for production)
- SQLite (for testing)

### Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="postgresql://user:password@localhost:5432/truss"
export PYTHONPATH="${PYTHONPATH}:$(pwd)/../.."
```

### Running the Service
```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Production mode
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Running Tests
```bash
# Run all tests
./run_tests.sh

# Run specific test file
pytest tests/test_logging.py -v

# Run with coverage
pytest tests/ --cov=. --cov-report=html
```

## Docker

### Build Image
```bash
docker build -t truss-logging .
```

### Run Container
```bash
docker run -p 8000:8000 \
  -e DATABASE_URL="****************************************/truss" \
  truss-logging
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `****************************************/truss` |
| `PYTHONPATH` | Python path for imports | `/app` |

## Usage Examples

### Create a Log Entry
```bash
curl -X POST "http://localhost:8000/log" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": 1,
    "user_id": 123,
    "role": "User",
    "action_type": "create_batch",
    "target_data": {"batch_id": 456, "name": "Test Batch"}
  }'
```

### Retrieve Logs
```bash
# Get all logs for tenant
curl "http://localhost:8000/logs?tenant_id=1"

# Get logs with filtering
curl "http://localhost:8000/logs?tenant_id=1&user_id=123&action_type=create_batch"

# Get logs with pagination
curl "http://localhost:8000/logs?tenant_id=1&page=1&page_size=10"
```

### Get Specific Log
```bash
curl "http://localhost:8000/logs/123"
```

## Architecture

The logging microservice follows a clean architecture pattern:

- **API Layer**: FastAPI endpoints for HTTP requests
- **Business Logic**: Log creation and retrieval logic
- **Data Layer**: SQLAlchemy ORM for database operations
- **Shared Components**: Reusable middleware and utilities

## Error Handling

The service provides comprehensive error handling:
- **400 Bad Request**: Invalid request data
- **404 Not Found**: Log entry not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server-side errors

## Monitoring

- Health check endpoint for container orchestration
- Structured logging with request/response details
- Database connection monitoring

## Security

- CORS middleware for cross-origin requests
- Input validation using Pydantic models
- SQL injection protection via SQLAlchemy ORM
- Tenant isolation for multi-tenancy
