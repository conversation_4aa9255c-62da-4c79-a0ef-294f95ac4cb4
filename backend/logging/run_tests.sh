#!/bin/bash

# Truss Logging Microservice Test Runner
# This script runs all tests for the logging microservice

set -e

echo "🧪 Running Truss Logging Microservice Tests"
echo "=========================================="

# Check if pytest is installed
if ! command -v pytest &> /dev/null; then
    echo "❌ pytest is not installed. Installing..."
    pip install pytest pytest-asyncio httpx
fi

# Set environment variables for testing
export PYTHONPATH="${PYTHONPATH}:$(pwd)/../.."

echo "🔧 Setting up test environment..."

# Run the tests
echo "🚀 Running tests..."
pytest tests/ -v --tb=short

echo "✅ All tests completed!"
