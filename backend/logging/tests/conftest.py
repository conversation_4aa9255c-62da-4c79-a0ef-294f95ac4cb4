import os
import sys
import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

# Add the parent directory to the path to import modules
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "..", ".."))

from backend.logging.main import app
from python.db import get_db


# Mock log entity for testing
class MockLogEntity:
    """Mock log entity that mimics the real LogEntity behavior"""

    def __init__(self, **kwargs):
        self.id = kwargs.get("id", 1)
        self.system_id = kwargs.get("system_id")
        self.user_id = kwargs.get("user_id")
        self.role = kwargs.get("role")
        self.action_type = kwargs.get("action_type")
        self.target_data = kwargs.get("target_data")
        self.timestamp = kwargs.get("timestamp", datetime.now(timezone.utc))


@pytest.fixture
def mock_db_session():
    """Create a mock database session"""
    mock_session = Mock(spec=Session)
    mock_session.add = Mock()
    mock_session.commit = Mock()
    mock_session.rollback = Mock()
    mock_session.refresh = Mock()
    mock_session.query = Mock()
    return mock_session


@pytest.fixture
def client(mock_db_session):
    """Create test client with mocked database"""
    app.dependency_overrides[get_db] = lambda: mock_db_session
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def sample_log_data():
    """Sample log data for testing"""
    return {
        "system_id": 1,
        "user_id": 1,
        "role": "User",
        "action_type": "create_batch",
        "target_data": {"batch_id": 123, "name": "Test Batch"},
        "timestamp": "2025-07-10T12:00:00Z",
    }


@pytest.fixture
def mock_log_entity():
    """Create a mock log entity for testing"""
    return MockLogEntity(
        id=1,
        system_id=1,
        user_id=1,
        role="User",
        action_type="create_batch",
        target_data={"batch_id": 123, "name": "Test Batch"},
        timestamp=datetime.now(timezone.utc),
    )
