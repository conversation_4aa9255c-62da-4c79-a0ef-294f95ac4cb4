from datetime import datetime, timezone
from unittest.mock import Mock, patch
from fastapi import status
from sqlalchemy.exc import SQLAlchemyError
import pytest


class TestHealthEndpoint:
    """Test health check endpoint"""

    def test_health_check(self, client):
        """Test health check returns healthy status"""
        response = client.get("/health")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"status": "healthy"}


class TestCreateLog:
    """Test log creation endpoint"""

    def test_create_log_success(
        self, client, mock_db_session, sample_log_data, mock_log_entity
    ):
        """Test successful log creation"""
        # Setup mock
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, "id", 1)

        response = client.post("/log", json=sample_log_data)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "logged"
        assert data["log_id"] == "1"

        # Verify database interactions
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

    def test_create_log_minimal_data(self, client, mock_db_session):
        """Test log creation with minimal required data"""
        log_data = {"system_id": 1, "action_type": "test_action"}
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, "id", 2)

        response = client.post("/log", json=log_data)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "logged"
        assert data["log_id"] == "2"

    def test_create_log_auto_timestamp(self, client, mock_db_session):
        """Test that timestamp is auto-generated when not provided"""
        log_data = {"system_id": 1, "action_type": "test_action"}
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, "id", 3)

        before_request = datetime.now(timezone.utc)
        response = client.post("/log", json=log_data)
        after_request = datetime.now(timezone.utc)

        assert response.status_code == status.HTTP_200_OK

        # Verify the log entity was created with a timestamp
        call_args = mock_db_session.add.call_args[0][0]
        assert hasattr(call_args, "timestamp")
        assert before_request <= call_args.timestamp <= after_request

    def test_create_log_database_error(self, client, mock_db_session, sample_log_data):
        """Test log creation with database error"""
        mock_db_session.commit.side_effect = SQLAlchemyError(
            "Database connection failed"
        )

        response = client.post("/log", json=sample_log_data)

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to create log entry" in response.json()["detail"]
        mock_db_session.rollback.assert_called_once()

    def test_create_log_invalid_data(self, client):
        """Test log creation with invalid data"""
        invalid_data = {"invalid_field": "invalid_value"}

        response = client.post("/log", json=invalid_data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestGetLogs:
    """Test log retrieval endpoints"""

    def test_get_logs_empty(self, client, mock_db_session):
        """Test getting logs when none exist"""
        # Setup mock to return empty query result
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 0
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        mock_db_session.query.return_value = mock_query

        response = client.get("/logs?system_id=1")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["logs"] == []
        assert data["total"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 50
        assert data["total_pages"] == 0

    def test_get_logs_with_data(self, client, mock_db_session, mock_log_entity):
        """Test getting logs with existing data"""
        # Setup mock to return one log
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 1
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_log_entity]
        mock_db_session.query.return_value = mock_query

        response = client.get("/logs?system_id=1")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["logs"]) == 1
        assert data["total"] == 1
        assert data["logs"][0]["system_id"] == mock_log_entity.system_id
        assert data["logs"][0]["action_type"] == mock_log_entity.action_type

    def test_get_logs_pagination(self, client, mock_db_session):
        """Test log pagination"""
        # Create mock logs
        mock_logs = [
            Mock(
                id=i,
                system_id=1,
                user_id=1,
                role="User",
                action_type=f"action_{i}",
                target_data={},
                timestamp=datetime.now(timezone.utc),
            )
            for i in range(5)
        ]

        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 5
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_logs[:2]  # First page with 2 items
        mock_db_session.query.return_value = mock_query

        response = client.get("/logs?system_id=1&page=1&page_size=2")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["logs"]) == 2
        assert data["total"] == 5
        assert data["page"] == 1
        assert data["page_size"] == 2
        assert data["total_pages"] == 3

    def test_get_logs_filter_by_user(self, client, mock_db_session, mock_log_entity):
        """Test filtering logs by user_id"""
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 1
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_log_entity]
        mock_db_session.query.return_value = mock_query

        response = client.get("/logs?system_id=1&user_id=1")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["logs"]) == 1
        assert data["logs"][0]["user_id"] == 1

    def test_get_logs_database_error(self, client, mock_db_session):
        """Test database error during log retrieval"""
        mock_db_session.query.side_effect = SQLAlchemyError(
            "Database connection failed"
        )

        response = client.get("/logs?system_id=1")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to retrieve logs" in response.json()["detail"]

    def test_get_logs_missing_system_id(self, client):
        """Test getting logs without system_id parameter"""
        response = client.get("/logs")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestGetLogById:
    """Test individual log retrieval"""

    def test_get_log_by_id_success(self, client, mock_db_session, mock_log_entity):
        """Test successful retrieval of log by ID"""
        # Setup mock to return the log entity
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_log_entity
        mock_db_session.query.return_value = mock_query

        response = client.get(f"/logs/{mock_log_entity.id}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == mock_log_entity.id
        assert data["system_id"] == mock_log_entity.system_id
        assert data["action_type"] == mock_log_entity.action_type

    def test_get_log_by_id_not_found(self, client, mock_db_session):
        """Test retrieval of non-existent log"""
        # Setup mock to return None (not found)
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        mock_db_session.query.return_value = mock_query

        response = client.get("/logs/99999")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "Log entry not found" in response.json()["detail"]

    def test_get_log_by_id_database_error(self, client, mock_db_session):
        """Test database error during log retrieval by ID"""
        mock_db_session.query.side_effect = SQLAlchemyError(
            "Database connection failed"
        )

        response = client.get("/logs/1")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to retrieve log" in response.json()["detail"]

    def test_get_log_by_id_invalid_id(self, client):
        """Test retrieval with invalid log ID"""
        response = client.get("/logs/invalid")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
