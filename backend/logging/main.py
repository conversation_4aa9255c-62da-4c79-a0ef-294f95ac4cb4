import os
import sys
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from pydantic import BaseModel
import uvicorn

# Optional debugging support
DEBUG_ENABLED = os.getenv("DEBUG_ENABLED", "false").lower() == "true"
DEBUG_PORT = int(os.getenv("DEBUG_LOGGING_PORT", "5688"))

if DEBUG_ENABLED:
    try:
        import debugpy

        debugpy.listen(("0.0.0.0", DEBUG_PORT))
        print(f"🐛 Logging Service: Debug server listening on port {DEBUG_PORT}")
        print("🐛 Waiting for debugger to attach...")
        debugpy.wait_for_client()
        print("🐛 Debugger attached!")
    except ImportError:
        print("⚠️ debugpy not installed, skipping debug setup")
    except Exception as e:
        print(f"⚠️ Debug setup failed: {e}")

# Add the parent directory to the path to import shared modules
repo_root = os.path.join(os.path.dirname(__file__), "..", "..")
sys.path.append(repo_root)
sys.path.append(os.path.join(repo_root, "shared"))

# Import shared components
from python.health import router as health_router
from python.logging_middleware import LoggingMiddleware
from python.db import get_db, engine


# For testing, we'll create a simple LogEntity class that can be mocked
# In production, this would import from the actual database models
class LogEntity:
    def __init__(self, **kwargs):
        self.id = kwargs.get("id")
        self.system_id = kwargs.get("system_id")
        self.user_id = kwargs.get("user_id")
        self.role = kwargs.get("role")
        self.action_type = kwargs.get("action_type")
        self.target_data = kwargs.get("target_data")
        self.timestamp = kwargs.get("timestamp", datetime.now(timezone.utc))


# Create FastAPI app
app = FastAPI(
    title="Truss Logging Microservice",
    description="Microservice for logging actions and errors in the Truss Robotic Management System",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Include health check router
app.include_router(health_router)


# Pydantic models for API requests/responses
class LogCreateRequest(BaseModel):
    """Request model for creating a log entry"""

    system_id: int
    user_id: Optional[int] = None
    role: Optional[str] = None
    action_type: Optional[str] = None
    target_data: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None


class LogResponse(BaseModel):
    """Response model for log entries"""

    id: int
    system_id: int
    user_id: Optional[int]
    role: Optional[str]
    action_type: Optional[str]
    target_data: Optional[Dict[str, Any]]
    timestamp: datetime


class LogListResponse(BaseModel):
    """Response model for paginated log list"""

    logs: List[LogResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# API Endpoints
@app.post("/log", response_model=Dict[str, str])
async def create_log(log_data: LogCreateRequest, db: Session = Depends(get_db)):
    """
    Create a new log entry
    """
    try:
        # Create new log entry
        log_entry = LogEntity(
            system_id=log_data.system_id,
            user_id=log_data.user_id,
            role=log_data.role,
            action_type=log_data.action_type,
            target_data=log_data.target_data,
            timestamp=log_data.timestamp or datetime.now(timezone.utc),
        )

        db.add(log_entry)
        db.commit()
        db.refresh(log_entry)

        return {"status": "logged", "log_id": str(log_entry.id)}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to create log entry: {str(e)}"
        )


@app.get("/logs", response_model=LogListResponse)
async def get_logs(
    system_id: int = Query(..., description="System ID to filter logs"),
    user_id: Optional[int] = Query(None, description="User ID to filter logs"),
    action_type: Optional[str] = Query(None, description="Action type to filter logs"),
    start_date: Optional[datetime] = Query(
        None, description="Start date for filtering logs"
    ),
    end_date: Optional[datetime] = Query(
        None, description="End date for filtering logs"
    ),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of logs per page"),
    db: Session = Depends(get_db),
):
    """
    Retrieve logs with filtering and pagination
    """
    try:
        # Build query with filters
        query = db.query(LogEntity).filter(LogEntity.system_id == system_id)

        if user_id is not None:
            query = query.filter(LogEntity.user_id == user_id)

        if action_type:
            query = query.filter(LogEntity.action_type == action_type)

        if start_date:
            query = query.filter(LogEntity.timestamp >= start_date)

        if end_date:
            query = query.filter(LogEntity.timestamp <= end_date)

        # Get total count
        total = query.count()

        # Apply pagination and ordering
        logs = (
            query.order_by(desc(LogEntity.timestamp))
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )

        # Convert to response models
        log_responses = [
            LogResponse(
                id=log.id,
                system_id=log.system_id,
                user_id=log.user_id,
                role=log.role,
                action_type=log.action_type,
                target_data=log.target_data,
                timestamp=log.timestamp,
            )
            for log in logs
        ]

        total_pages = (total + page_size - 1) // page_size

        return LogListResponse(
            logs=log_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve logs: {str(e)}"
        )


@app.get("/logs/{log_id}", response_model=LogResponse)
async def get_log(log_id: int, db: Session = Depends(get_db)):
    """
    Retrieve a specific log entry by ID
    """
    try:
        log = db.query(LogEntity).filter(LogEntity.id == log_id).first()

        if not log:
            raise HTTPException(status_code=404, detail="Log entry not found")

        return LogResponse(
            id=log.id,
            system_id=log.system_id,
            user_id=log.user_id,
            role=log.role,
            action_type=log.action_type,
            target_data=log.target_data,
            timestamp=log.timestamp,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve log: {str(e)}")


if __name__ == "__main__":
    port = int(os.getenv("LOGGING_SERVICE_PORT", "8011"))
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=True, log_level="info")
