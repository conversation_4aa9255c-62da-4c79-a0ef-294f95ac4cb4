{
  // Python Configuration
  "python.defaultInterpreterPath": "../venv/bin/python",
  "python.terminal.activateEnvironment": true,
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "python.testing.pytestArgs": ["."],
  "python.testing.autoTestDiscoverOnSaveEnabled": true,

  // Python Linting and Formatting
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length", "88"],
  "python.sortImports.args": ["--profile", "black"],

  // Editor Configuration
  "editor.formatOnSave": true,
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    }
  },

  // File Associations
  "files.associations": {
    "test_*.py": "python",
    "*_test.py": "python",
    "conftest.py": "python"
  },

  // Search Configuration
  "search.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    "**/coverage": true,
    "**/.pytest_cache": true,
    "**/.mypy_cache": true,
    "**/htmlcov": true
  },

  // File Watcher Configuration
  "files.watcherExclude": {
    "**/__pycache__/**": true,
    "**/.pytest_cache/**": true,
    "**/.mypy_cache/**": true,
    "**/htmlcov/**": true
  },

  // Terminal Configuration
  "terminal.integrated.env.linux": {
    "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/shared/python",
    "DATABASE_URL": "sqlite:///test.db"
  },

  // Test Explorer Configuration
  "python.testing.cwd": "${workspaceFolder}",
  "python.testing.pytestPath": "pytest",

  // Auto Save Configuration
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,

  // Editor Configuration
  "editor.tabSize": 4,
  "editor.insertSpaces": true,
  "editor.rulers": [88],

  // Coverage Configuration
  "coverage-gutters.coverageFileNames": [
    "coverage.xml",
    "coverage.lcov",
    "cov.xml",
    "coverage.json"
  ],
  "coverage-gutters.showLineCoverage": true,
  "coverage-gutters.showRulerCoverage": true
}
