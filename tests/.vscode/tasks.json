{"version": "2.0.0", "tasks": [{"label": "🧪 Run All Tests", "type": "shell", "command": "pytest", "args": ["-v", "--tb=short"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}}, "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🧪 Run Unit Tests", "type": "shell", "command": "pytest", "args": ["-v", "--tb=short", "unit/"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🧪 Run Integration Tests", "type": "shell", "command": "pytest", "args": ["-v", "--tb=short", "integration/"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5434/tom_test", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🧪 Run E2E Tests", "type": "shell", "command": "pytest", "args": ["-v", "--tb=short", "e2e/"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5434/tom_test", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python", "API_BASE_URL": "http://localhost:4000", "FRONTEND_BASE_URL": "http://localhost:3000"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}, {"label": "🧪 Run Tests with Coverage", "type": "shell", "command": "pytest", "args": ["-v", "--tb=short", "--cov=.", "--cov-report=html", "--cov-report=term"], "options": {"cwd": "${workspaceFolder}", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "group": "testing"}, "problemMatcher": []}]}