{"version": "0.2.0", "configurations": [{"name": "🧪 Debug All Python Tests", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "--tb=short", "${workspaceFolder}"], "cwd": "${workspaceFolder}", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "testing", "order": 1}}, {"name": "🧪 Debug Integration Tests", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "--tb=short", "${workspaceFolder}/integration"], "cwd": "${workspaceFolder}", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom_test", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "testing", "order": 2}}, {"name": "🧪 Debug Unit Tests", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "--tb=short", "${workspaceFolder}/unit"], "cwd": "${workspaceFolder}", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "testing", "order": 3}}, {"name": "🧪 Debug E2E Tests", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "--tb=short", "${workspaceFolder}/e2e"], "cwd": "${workspaceFolder}", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "postgresql://user:password@localhost:5442/tom_test", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python", "API_BASE_URL": "http://localhost:4010", "FRONTEND_BASE_URL": "http://localhost:3010"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "testing", "order": 4}}, {"name": "🧪 Debug Current Test File", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "--tb=short", "${file}"], "cwd": "${workspaceFolder}", "envFile": "${workspaceRoot}/.env", "env": {"DATABASE_URL": "sqlite:///test.db", "PYTHONPATH": "${workspaceRoot}:${workspaceRoot}/shared/python"}, "console": "integratedTerminal", "justMyCode": false, "presentation": {"hidden": false, "group": "testing", "order": 5}}]}