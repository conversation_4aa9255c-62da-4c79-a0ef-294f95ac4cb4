from pydantic import BaseModel
from typing import Dict, Any, List, Optional


class Dimensions(BaseModel):
    thickness: Optional[float] = None
    height: Optional[float] = None
    length: Optional[float] = None
    short_length: Optional[float] = None
    long_length: Optional[float] = None
    top_length: Optional[float] = None
    center_length: Optional[float] = None
    bottom_length: Optional[float] = None
    nominal_length: Optional[float] = None
    inside_length: Optional[float] = None


class Board(BaseModel):
    id: int
    truss_id: int
    system_id: int
    piece_type: Optional[str] = None
    dimensions: Dimensions
    original_dimensions: Dimensions
    vertices: Dict[str, Any]
    original_vertices: Dict[str, Any]
    label: Optional[str] = None
    member_grade: Optional[str] = None
    member_species: Optional[str] = None
    object_guid: Optional[str] = None
    name: Optional[str] = None
    attached_plates: Optional[List[int]] = None
    intersecting_plates: Optional[List[int]] = None
    splice_plate_id: Optional[int] = None
    manual: bool = False
    direction: Optional[Dict[str, Any]] = None
    place_location: Optional[Dict[str, Any]] = None
    plate_edges: Optional[Dict[str, Any]] = None
    pick_offset: Optional[Dict[str, Any]] = None
    center_plate_ids: Optional[List[int]] = None
    print_label: Optional[str] = None
    angle_limits: Optional[Dict[str, Any]] = None
    weight: Optional[float] = None
    notes: Optional[str] = None
    angle: Optional[float] = None
    file_vertices: Optional[Dict[str, Any]] = None
    file_rotation: Optional[Dict[str, Any]] = None
    file_order: Optional[int] = None
    modifications: List[Dict[str, Any]] = []
