from pydantic import BaseModel
from typing import Dict, Any, List, Optional


class Plate(BaseModel):
    id: int
    truss_id: int
    system_id: int
    unique_id: Optional[str] = None
    gauge: Optional[float] = None
    description: Optional[str] = None
    plate_height: Optional[float] = None
    plate_width: Optional[float] = None
    vertices: Dict[str, Any]
    original_vertices: Dict[str, Any]
    label: Optional[str] = None
    offset: Dict[str, Any]
    original_offset: Dict[str, Any]
    center_point: Dict[str, Any]
    original_center_point: Dict[str, Any]
    object_guid: Optional[str] = None
    dimensions: Dict[str, Any]
    original_dimensions: Dict[str, Any]
    rotation: Dict[str, Any]
    original_rotation: Dict[str, Any]
    file_center_point: Optional[Dict[str, Any]] = None
    file_rotation: Optional[Dict[str, Any]] = None
    file_vertices: Optional[Dict[str, Any]] = None
    manual: bool = False
    modifications: List[Dict[str, Any]] = []
