// TypeScript DTOs generated from Pydantic models for Task Orchestration Manager (TOM)

export interface System {
  id: number;
  name: string;
}

export interface Log {
  id: number;
  system_id: number;
  user_id?: number;
  role?: string;
  action_type?: string;
  target_data?: { [key: string]: any };
  timestamp: string;
}

export interface User {
  id: number;
  system_id: number;
  username: string;
  pin: string;
  role: string;
}

export interface Setting {
  id: number;
  system_id: number;
  key: string;
  value: { [key: string]: any };
  version: number;
}

export interface Tag {
  id: number;
  name: string;
}

export enum EntityType {
  batch = "batch",
}

export interface TagsRelation {
  id: number;
  entity_type: EntityType;
  type_id: number;
  tag_id: number;
}

export interface TrussFile {
  id: number;
  system_id: number;
  file_data: string; // Base64-encoded bytes
  created_at: string;
}

export interface Batch {
  id: number;
  system_id: number;
  name: string;
  description?: string;
  status: string;
}

export interface Truss {
  id: number;
  batch_id: number;
  system_id: number;
  metadata?: { [key: string]: any };
}

export interface Dimensions {
  thickness?: number;
  height?: number;
  length?: number;
  short_length?: number;
  long_length?: number;
  top_length?: number;
  center_length?: number;
  bottom_length?: number;
  nominal_length?: number;
  inside_length?: number;
}

export interface Board {
  id: number;
  truss_id: number;
  system_id: number;
  piece_type?: string;
  dimensions: Dimensions;
  original_dimensions: Dimensions;
  vertices: { [key: string]: any };
  original_vertices: { [key: string]: any };
  label?: string;
  member_grade?: string;
  member_species?: string;
  object_guid?: string;
  name?: string;
  attached_plates?: number[];
  intersecting_plates?: number[];
  splice_plate_id?: number;
  manual: boolean;
  direction?: { [key: string]: any };
  place_location?: { [key: string]: any };
  plate_edges?: { [key: string]: any };
  pick_offset?: { [key: string]: any };
  center_plate_ids?: number[];
  print_label?: string;
  angle_limits?: { [key: string]: any };
  weight?: number;
  notes?: string;
  angle?: number;
  file_vertices?: { [key: string]: any };
  file_rotation?: { [key: string]: any };
  file_order?: number;
  modifications: { [key: string]: any }[];
}

export interface Plate {
  id: number;
  truss_id: number;
  system_id: number;
  unique_id?: string;
  gauge?: number;
  description?: string;
  plate_height?: number;
  plate_width?: number;
  vertices: { [key: string]: any };
  original_vertices: { [key: string]: any };
  label?: string;
  offset: { [key: string]: any };
  original_offset: { [key: string]: any };
  center_point: { [key: string]: any };
  original_center_point: { [key: string]: any };
  object_guid?: string;
  dimensions: { [key: string]: any };
  original_dimensions: { [key: string]: any };
  rotation: { [key: string]: any };
  original_rotation: { [key: string]: any };
  file_center_point?: { [key: string]: any };
  file_rotation?: { [key: string]: any };
  file_vertices?: { [key: string]: any };
  manual: boolean;
  modifications: { [key: string]: any }[];
}

export interface TrussBuild {
  id: number;
  truss_id: number;
  system_id: number;
  build_timestamp: string;
  boards_snapshot: { [key: string]: any }[];
  plates_snapshot: { [key: string]: any }[];
  robot_data: { [key: string]: any };
}
