# TOM Port Configuration

This document outlines all the non-standard ports used in the TOM (Task Orchestration Manager) project to avoid conflicts with common services.

## 🚪 Port Mapping Overview

### **Infrastructure Services**
| Service    | Standard Port | TOM Port | Reason for Change |
|------------|---------------|----------|-------------------|
| PostgreSQL | 5432         | **5442** | Avoid conflicts with other PostgreSQL instances |
| Redis      | 6379         | **6389** | Avoid conflicts with other Redis instances |

### **Application Services**
| Service                 | Standard Port | TOM Port | Reason for Change |
|-------------------------|---------------|----------|-------------------|
| API Server              | 3000/4000    | **4010** | Avoid conflicts with React dev servers |
| Main UI (React)         | 3000         | **3010** | Avoid conflicts with other React apps |
| Board Display (React)   | 3001         | **3011** | Avoid conflicts with other frontend apps |

### **Python Microservices**
| Service                 | Standard Port | TOM Port | Reason for Change |
|-------------------------|---------------|----------|-------------------|
| Logging Service         | 8000/8001    | **8011** | Avoid conflicts with other Python services |
| Settings Service        | 8000/8002    | **8012** | Avoid conflicts with other Python services |
| File Parsing Service    | 8000/8003    | **8013** | Avoid conflicts with other Python services |
| Rules Engine Service    | 8000/8004    | **8014** | Avoid conflicts with other Python services |
| Robotic Integration     | 8000/8005    | **8015** | Avoid conflicts with other Python services |

### **Debug Ports**
| Service                 | Standard Port | TOM Port | Reason for Change |
|-------------------------|---------------|----------|-------------------|
| Node.js API Debug      | 9229         | **9239** | Avoid conflicts with other Node.js debuggers |
| Logging Debug (debugpy) | 5678         | **5688** | Avoid conflicts with other Python debuggers |
| Settings Debug          | 5679         | **5689** | Avoid conflicts with other Python debuggers |
| File Parsing Debug      | 5680         | **5690** | Avoid conflicts with other Python debuggers |
| Rules Engine Debug      | 5681         | **5691** | Avoid conflicts with other Python debuggers |
| Robotic Integration Debug | 5682       | **5692** | Avoid conflicts with other Python debuggers |

## 🔧 Configuration Files Updated

### **Environment Variables (`.env`)**
```env
# Database
DB_PORT=5442
DATABASE_URL=postgresql://user:password@localhost:5442/tom

# Redis
REDIS_PORT=6389
REDIS_URL=redis://localhost:6389

# API
API_PORT=4010

# Frontend
MAIN_UI_PORT=3010
BOARD_DISPLAY_PORT=3011
API_URL=http://localhost:4010

# Python Services
LOGGING_SERVICE_PORT=8011
SETTINGS_SERVICE_PORT=8012
FILE_PARSING_SERVICE_PORT=8013
RULES_ENGINE_SERVICE_PORT=8014
ROBOTIC_INTEGRATION_SERVICE_PORT=8015

# Debug Ports
DEBUG_API_PORT=9239
DEBUG_LOGGING_PORT=5688
DEBUG_SETTINGS_PORT=5689
DEBUG_FILE_PARSING_PORT=5690
DEBUG_RULES_ENGINE_PORT=5691
DEBUG_ROBOTIC_INTEGRATION_PORT=5692
```

### **Docker Compose Files**
- `deployment/docker-compose.yml` - Infrastructure ports updated
- `deployment/docker-compose.prod.yml` - Production application ports updated

### **VS Code Configurations**
- All `launch.json` files updated with new ports
- All `tasks.json` files updated with new ports
- Environment variables in debug configurations updated

### **Python Service Files**
- `backend/logging/main.py` - Updated default ports
- `backend/settings/main.py` - Updated default ports
- Other Python services will need similar updates

## 🌐 Access URLs

### **Development Environment**
- **Main UI**: http://localhost:3010
- **Board Display**: http://localhost:3011
- **API Server**: http://localhost:4010
- **API Documentation**: http://localhost:4010/docs (FastAPI auto-docs)
- **Logging Service**: http://localhost:8011
- **Settings Service**: http://localhost:8012
- **File Parsing Service**: http://localhost:8013
- **Rules Engine Service**: http://localhost:8014
- **Robotic Integration Service**: http://localhost:8015

### **Infrastructure**
- **PostgreSQL**: localhost:5442
- **Redis**: localhost:6389

### **Debug Connections**
- **Node.js API**: localhost:9239
- **Python Services**: localhost:5688-5692

## 🔍 Port Conflict Checking

Before starting development, check if these ports are available:

```bash
# Check infrastructure ports
lsof -i :5442  # PostgreSQL
lsof -i :6389  # Redis

# Check application ports
lsof -i :3010  # Main UI
lsof -i :3011  # Board Display
lsof -i :4010  # API Server

# Check microservice ports
lsof -i :8011  # Logging Service
lsof -i :8012  # Settings Service
lsof -i :8013  # File Parsing Service
lsof -i :8014  # Rules Engine Service
lsof -i :8015  # Robotic Integration Service

# Check debug ports
lsof -i :9239  # Node.js Debug
lsof -i :5688  # Logging Debug
lsof -i :5689  # Settings Debug
lsof -i :5690  # File Parsing Debug
lsof -i :5691  # Rules Engine Debug
lsof -i :5692  # Robotic Integration Debug
```

## 🚨 Common Port Conflicts Avoided

### **Standard Ports We're Avoiding**
- **3000**: Create React App default
- **3001**: Next.js default alternate
- **4000**: Common API server port
- **5432**: PostgreSQL default
- **6379**: Redis default
- **8000**: Python development server default
- **8001-8005**: Common microservice ports
- **9229**: Node.js inspector default
- **5678-5682**: Common Python debugpy ports

### **Benefits of Non-Standard Ports**
1. **No conflicts** with other development projects
2. **Team consistency** - everyone uses the same ports
3. **Easy identification** - TOM services are clearly distinguishable
4. **Parallel development** - can run TOM alongside other projects
5. **Production safety** - less likely to conflict with system services

## 📝 Notes

- All ports are configured in the shared `.env` file for consistency
- VS Code launch configurations automatically use these ports
- Docker Compose files map external ports to standard internal ports
- Debug ports are only used during development debugging sessions
- Production deployments can use standard ports internally within containers
